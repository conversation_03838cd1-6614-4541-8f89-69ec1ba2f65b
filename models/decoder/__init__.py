# from .detr3d_decoder import Detr3DDecoder
# from .grasp_embed_decoder import GraspKDecoder
# from .vanilla_decoder import VanillaDecoder
# from .task_decoder import GraspTaskDecoder
# from .unet import UNetModel
from .unet_new import UNetModel

def build_decoder(decoder_cfg):
    if decoder_cfg.name.lower() == "task":
        # return GraspTaskDecoder(decoder_cfg)
        raise NotImplementedError(f"No such decoder: {decoder_cfg.name}")
    if decoder_cfg.name.lower() == "3detr":
        # return Detr3DDecoder(decoder_cfg)
        raise NotImplementedError(f"No such decoder: {decoder_cfg.name}")
    elif decoder_cfg.name.lower() == "vanilla":
        # return VanillaDecoder(decoder_cfg)
        raise NotImplementedError(f"No such decoder: {decoder_cfg.name}")
    elif decoder_cfg.name.lower() == "graspk":
        # return GraspKDecoder(decoder_cfg)
        raise NotImplementedError(f"No such decoder: {decoder_cfg.name}")
    elif decoder_cfg.name.lower() == "unet":
        return UNetModel(decoder_cfg)
    else:
        raise NotImplementedError(f"No such decode: {decoder_cfg.name}")
