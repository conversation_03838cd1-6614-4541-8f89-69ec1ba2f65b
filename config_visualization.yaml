# 预测与真实抓取对比可视化配置文件

# 数据集配置
dataset:
  # 数据集根目录
  root_dir: "/home/<USER>/source/grasp/SceneLeapPro/data/520_0_sub_3"
  # 成功抓取数据目录
  succ_grasp_dir: "/home/<USER>/source/grasp/SceneLeapPro/data/succ_collect"
  # 物体模型根目录
  obj_root_dir: "/home/<USER>/source/grasp/SceneLeapPro/data/object/objaverse_v1/flat_models"
  
  # 数据集参数
  num_grasps: 8                    # 每个样本的抓取数量
  max_grasps_per_object: 2         # 每个物体的最大抓取数量 (测试时使用较小值)
  mode: "camera_centric_scene_mean_normalized"  # 坐标系模式
  mesh_scale: 0.1                  # mesh缩放因子
  num_neg_prompts: 4               # 负面提示词数量
  enable_cropping: true            # 是否启用裁剪
  max_points: 20000                # 最大点云数量
  grasp_sampling_strategy: "random"  # 抓取采样策略

# 模型配置
model:
  # 预训练模型checkpoint路径 (需要用户修改为实际路径)
  checkpoint_path: "experiments/test_test_test81_0/checkpoints/epoch=16-val_loss=27.73.ckpt"
  # 配置文件路径 (可选，如果为null则自动推断)
  config_path: "experiments/test_test_test81_0/config/whole_config.yaml"
  # 计算设备
  device: "cuda"  # 或 "cpu"

# 可视化配置
visualization:
  # 要可视化的样本索引
  sample_idx: 0
  # 最大显示抓取数量
  max_grasps_to_show: 3
  # 窗口大小
  window_width: 1400
  window_height: 900
  # 坐标轴大小
  coordinate_frame_size: 0.1
  
  # 颜色配置
  colors:
    # 预测抓取颜色 (蓝色系)
    prediction:
      - [0.0, 0.0, 1.0]    # 蓝色
      - [0.0, 0.5, 1.0]    # 浅蓝色
      - [0.0, 1.0, 1.0]    # 青色
      - [0.5, 0.0, 1.0]    # 紫蓝色
      - [0.0, 0.0, 0.5]    # 深蓝色
    
    # 真实抓取颜色 (红色系)
    ground_truth:
      - [1.0, 0.0, 0.0]    # 红色
      - [1.0, 0.5, 0.0]    # 橙色
      - [1.0, 0.0, 0.5]    # 粉红色
      - [0.8, 0.0, 0.0]    # 深红色
      - [1.0, 0.2, 0.2]    # 浅红色
    
    # 物体mesh颜色
    object_mesh: [0.0, 0.8, 0.0]    # 绿色
    # 目标物体点云颜色
    object_points: [1.0, 0.0, 0.0]  # 红色

# 批量分析配置
batch_analysis:
  # 是否启用批量分析
  enabled: true
  # 分析的样本数量
  num_samples: 5
  # 是否保存分析结果到文件
  save_results: true
  # 结果保存路径
  results_path: "analysis_results.json"

# 日志配置
logging:
  # 日志级别
  level: "INFO"
  # 是否显示详细的误差信息
  show_detailed_errors: true
  # 是否显示进度条
  show_progress: true
