#!/usr/bin/env python3
"""
实验管理脚本
用于自动运行多组实验并分析结果
"""

import os
import sys
import subprocess
import json
import yaml
import argparse
from pathlib import Path
from typing import Dict, List, Any
import logging
from datetime import datetime
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ExperimentManager:
    def __init__(self, config_path: str = "config/experiments/experiment_sweep.yaml"):
        self.config_path = Path(config_path)
        self.experiments_dir = Path("experiments")
        self.results_dir = Path("experiment_results")
        self.results_dir.mkdir(exist_ok=True)
        
        # 加载实验配置
        self.load_config()
        
    def load_config(self):
        """加载实验配置"""
        if not self.config_path.exists():
            logger.error(f"配置文件不存在: {self.config_path}")
            return
            
        with open(self.config_path, 'r', encoding='utf-8') as f:
            self.config = yaml.safe_load(f)
            
        logger.info(f"加载实验配置: {self.config_path}")
        
    def generate_experiment_commands(self, group_name: str) -> List[Dict[str, Any]]:
        """生成实验命令列表"""
        if group_name not in self.config.get('experiment_groups', {}):
            logger.error(f"实验组 {group_name} 不存在")
            return []
            
        group_config = self.config['experiment_groups'][group_name]
        parameters = group_config['parameters']
        
        # 生成参数组合
        import itertools
        param_names = list(parameters.keys())
        param_values = list(parameters.values())
        
        experiments = []
        for i, combination in enumerate(itertools.product(*param_values)):
            experiment_params = dict(zip(param_names, combination))
            experiment_name = f"{group_name}_{i+1}"
            
            # 构建Hydra命令
            cmd_parts = ["python", "train_lightning.py"]
            
            # 添加参数覆盖
            for param_name, param_value in experiment_params.items():
                cmd_parts.append(f"{param_name}={param_value}")
                
            # 添加实验特定配置
            cmd_parts.extend([
                f"save_root=./experiments/{experiment_name}",
                f"wandb.name={experiment_name}",
                f"wandb.group={group_name}"
            ])
            
            experiments.append({
                'name': experiment_name,
                'group': group_name,
                'parameters': experiment_params,
                'command': cmd_parts,
                'description': group_config.get('description', '')
            })
            
        return experiments
        
    def run_experiment(self, experiment: Dict[str, Any], dry_run: bool = False) -> bool:
        """运行单个实验"""
        logger.info(f"运行实验: {experiment['name']}")
        logger.info(f"参数: {experiment['parameters']}")
        
        if dry_run:
            logger.info(f"命令: {' '.join(experiment['command'])}")
            return True
            
        try:
            # 创建实验目录
            exp_dir = self.experiments_dir / experiment['name']
            exp_dir.mkdir(parents=True, exist_ok=True)
            
            # 保存实验配置
            with open(exp_dir / "experiment_config.json", 'w') as f:
                json.dump(experiment, f, indent=2)
                
            # 运行实验
            result = subprocess.run(
                experiment['command'],
                capture_output=True,
                text=True,
                cwd=Path.cwd()
            )
            
            if result.returncode == 0:
                logger.info(f"实验 {experiment['name']} 成功完成")
                return True
            else:
                logger.error(f"实验 {experiment['name']} 失败: {result.stderr}")
                return False
                
        except Exception as e:
            logger.error(f"运行实验 {experiment['name']} 时出错: {e}")
            return False
            
    def run_experiment_group(self, group_name: str, dry_run: bool = False, max_parallel: int = 1):
        """运行实验组"""
        logger.info(f"开始运行实验组: {group_name}")
        
        experiments = self.generate_experiment_commands(group_name)
        if not experiments:
            logger.error(f"没有找到实验组: {group_name}")
            return
            
        logger.info(f"找到 {len(experiments)} 个实验")
        
        if dry_run:
            for exp in experiments:
                logger.info(f"实验: {exp['name']}")
                logger.info(f"命令: {' '.join(exp['command'])}")
                logger.info("-" * 50)
            return
            
        # 运行实验
        successful = 0
        failed = 0
        
        for i, experiment in enumerate(experiments):
            logger.info(f"进度: {i+1}/{len(experiments)}")
            
            if self.run_experiment(experiment, dry_run):
                successful += 1
            else:
                failed += 1
                
        logger.info(f"实验组 {group_name} 完成: 成功 {successful}, 失败 {failed}")
        
    def analyze_results(self, group_name: str):
        """分析实验结果"""
        logger.info(f"分析实验组 {group_name} 的结果")
        
        # 查找实验结果
        group_dir = self.experiments_dir
        results = []
        
        for exp_dir in group_dir.iterdir():
            if exp_dir.is_dir() and exp_dir.name.startswith(group_name):
                # 查找验证结果
                val_results = list(exp_dir.rglob("*.json"))
                if val_results:
                    with open(val_results[0], 'r') as f:
                        result_data = json.load(f)
                        results.append({
                            'experiment': exp_dir.name,
                            'results': result_data
                        })
                        
        if not results:
            logger.warning(f"没有找到实验组 {group_name} 的结果")
            return
            
        # 创建结果分析
        self.create_analysis_report(group_name, results)
        
    def create_analysis_report(self, group_name: str, results: List[Dict]):
        """创建分析报告"""
        report_dir = self.results_dir / group_name
        report_dir.mkdir(exist_ok=True)
        
        # 提取关键指标
        metrics_data = []
        for result in results:
            exp_name = result['experiment']
            exp_results = result['results']
            
            # 提取最终指标
            final_metrics = {}
            for key, value in exp_results.items():
                if isinstance(value, dict) and 'val' in key:
                    final_metrics[key] = value.get('epoch', {}).get('last', 0)
                    
            metrics_data.append({
                'experiment': exp_name,
                **final_metrics
            })
            
        # 保存为CSV
        df = pd.DataFrame(metrics_data)
        csv_path = report_dir / f"{group_name}_results.csv"
        df.to_csv(csv_path, index=False)
        logger.info(f"结果保存到: {csv_path}")
        
        # 创建可视化
        self.create_visualizations(group_name, df, report_dir)
        
    def create_visualizations(self, group_name: str, df: pd.DataFrame, report_dir: Path):
        """创建可视化图表"""
        plt.style.use('seaborn-v0_8')
        
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
        
        # 1. 损失曲线对比
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle(f'实验组 {group_name} 结果分析', fontsize=16)
        
        # 总损失对比
        if 'val/total_loss' in df.columns:
            axes[0, 0].bar(df['experiment'], df['val/total_loss'])
            axes[0, 0].set_title('验证总损失')
            axes[0, 0].set_ylabel('损失值')
            axes[0, 0].tick_params(axis='x', rotation=45)
            
        # 平移误差对比
        if 'val/translation_error' in df.columns:
            axes[0, 1].bar(df['experiment'], df['val/translation_error'])
            axes[0, 1].set_title('平移误差')
            axes[0, 1].set_ylabel('误差 (米)')
            axes[0, 1].tick_params(axis='x', rotation=45)
            
        # 旋转误差对比
        if 'val/rotation_error' in df.columns:
            axes[1, 0].bar(df['experiment'], df['val/rotation_error'])
            axes[1, 0].set_title('旋转误差')
            axes[1, 0].set_ylabel('误差 (度)')
            axes[1, 0].tick_params(axis='x', rotation=45)
            
        # 关节角度误差对比
        if 'val/qpos_error' in df.columns:
            axes[1, 1].bar(df['experiment'], df['val/qpos_error'])
            axes[1, 1].set_title('关节角度误差')
            axes[1, 1].set_ylabel('误差 (度)')
            axes[1, 1].tick_params(axis='x', rotation=45)
            
        plt.tight_layout()
        plt.savefig(report_dir / f"{group_name}_analysis.png", dpi=300, bbox_inches='tight')
        plt.close()
        
        logger.info(f"可视化图表保存到: {report_dir / f'{group_name}_analysis.png'}")
        
    def list_experiment_groups(self):
        """列出所有实验组"""
        groups = self.config.get('experiment_groups', {})
        logger.info("可用的实验组:")
        for name, config in groups.items():
            logger.info(f"  - {name}: {config.get('description', '无描述')}")
            
    def get_best_experiment(self, group_name: str, metric: str = "val/total_loss"):
        """获取最佳实验"""
        report_dir = self.results_dir / group_name
        csv_path = report_dir / f"{group_name}_results.csv"
        
        if not csv_path.exists():
            logger.error(f"结果文件不存在: {csv_path}")
            return None
            
        df = pd.read_csv(csv_path)
        if metric not in df.columns:
            logger.error(f"指标 {metric} 不存在")
            return None
            
        best_idx = df[metric].idxmin()
        best_exp = df.iloc[best_idx]
        
        logger.info(f"最佳实验: {best_exp['experiment']}")
        logger.info(f"指标 {metric}: {best_exp[metric]}")
        
        return best_exp.to_dict()

def main():
    parser = argparse.ArgumentParser(description="实验管理工具")
    parser.add_argument("--config", default="config/experiments/experiment_sweep.yaml", 
                       help="实验配置文件路径")
    parser.add_argument("--group", required=True, help="实验组名称")
    parser.add_argument("--dry-run", action="store_true", help="仅显示命令，不实际运行")
    parser.add_argument("--analyze", action="store_true", help="分析实验结果")
    parser.add_argument("--list", action="store_true", help="列出所有实验组")
    parser.add_argument("--best", action="store_true", help="获取最佳实验")
    parser.add_argument("--metric", default="val/total_loss", help="用于选择最佳实验的指标")
    
    args = parser.parse_args()
    
    manager = ExperimentManager(args.config)
    
    if args.list:
        manager.list_experiment_groups()
    elif args.analyze:
        manager.analyze_results(args.group)
    elif args.best:
        manager.get_best_experiment(args.group, args.metric)
    else:
        manager.run_experiment_group(args.group, args.dry_run)

if __name__ == "__main__":
    main() 