#!/bin/bash

# 分布式训练诊断和修复脚本
# 用于诊断多卡训练卡住的问题

echo "🔍 SceneLeapPlus 分布式训练诊断工具"
echo "=================================="

# 检查当前运行的训练进程
echo "1. 检查运行中的训练进程..."
TRAIN_PIDS=$(ps aux | grep train_lightning.py | grep -v grep | awk '{print $2}')
if [ -n "$TRAIN_PIDS" ]; then
    echo "✅ 发现运行中的训练进程:"
    ps aux | grep train_lightning.py | grep -v grep | head -n 5
    echo ""
    
    # 检查进程状态
    echo "📊 进程详细状态:"
    for pid in $TRAIN_PIDS; do
        if [ -d "/proc/$pid" ]; then
            echo "  PID $pid:"
            echo "    状态: $(cat /proc/$pid/stat | cut -d' ' -f3)"
            echo "    CPU时间: $(cat /proc/$pid/stat | cut -d' ' -f14-15)"
            echo "    内存: $(cat /proc/$pid/status | grep VmRSS)"
        fi
    done
    echo ""
else
    echo "❌ 没有发现运行中的训练进程"
    exit 1
fi

# 检查GPU使用情况
echo "2. 检查GPU使用情况..."
if command -v nvidia-smi &> /dev/null; then
    nvidia-smi --query-gpu=index,name,memory.used,memory.total,utilization.gpu --format=csv,noheader,nounits
else
    echo "❌ nvidia-smi 不可用"
fi
echo ""

# 检查NCCL日志
echo "3. 检查NCCL通信状态..."
MAIN_PID=$(echo $TRAIN_PIDS | cut -d' ' -f1)
if [ -n "$MAIN_PID" ]; then
    echo "主进程PID: $MAIN_PID"
    # 检查进程是否卡在某个系统调用上
    if command -v strace &> /dev/null; then
        echo "📝 检查主进程系统调用 (5秒采样):"
        timeout 5s strace -p $MAIN_PID 2>&1 | head -n 10 || echo "无法追踪进程 (可能需要权限)"
    fi
fi
echo ""

# 检查网络端口
echo "4. 检查分布式训练端口..."
netstat -ln | grep -E ':(29500|29501|29502)' || echo "未发现常用的分布式训练端口"
echo ""

# 检查资源使用
echo "5. 检查系统资源..."
echo "CPU负载: $(uptime | cut -d',' -f3-5)"
echo "内存使用: $(free -h | grep Mem)"
echo "磁盘IO: $(iostat -x 1 1 2>/dev/null | tail -n +4 | head -n 5)" || echo "iostat 不可用"
echo ""

# 提供解决方案
echo "🔧 可能的解决方案:"
echo "=================================="

echo "如果训练卡在数据加载阶段:"
echo "1. 终止当前训练: kill $TRAIN_PIDS"
echo "2. 减少worker数量重新启动:"
echo "   ./train_distributed.sh --gpus 4 data.train.num_workers=8 data.val.num_workers=8"
echo ""

echo "如果是数据集路径问题:"
echo "1. 检查数据路径是否存在:"
echo "   ls -la /home/<USER>/source/grasp/SceneLeapPro/data/test_final_test_520_1_processed"
echo "2. 检查缓存目录权限"
echo ""

echo "如果是内存不足:"
echo "1. 减少batch_size: ./train_distributed.sh --gpus 4 batch_size=32"
echo "2. 禁用数据预取: ./train_distributed.sh --gpus 4 data.train.num_workers=4"
echo ""

echo "如果是NCCL通信问题:"
echo "1. 设置NCCL调试: export NCCL_DEBUG=INFO"
echo "2. 切换到gloo后端: ./train_distributed.sh --gpus 4 distributed.backend=gloo"
echo ""

echo "强制终止所有训练进程:"
echo "pkill -f train_lightning.py"
echo ""

echo "📝 日志检查命令:"
echo "tail -f lightning_logs/version_*/events.out.tfevents.*"
echo "或查看最新的日志目录内容"
