import json
import logging
import os
from pathlib import Path

import hydra
import pytorch_lightning as pl
import wandb
from omegaconf import OmegaConf
from pytorch_lightning.callbacks import <PERSON>Checkpoint, ModelSummary, RichProgressBar
from pytorch_lightning.callbacks.progress.rich_progress import RichProgressBarTheme
from pytorch_lightning.loggers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TensorBoardLogger

from datasets.scenedex_datamodule import SceneLeapDataModule
from models.diffuser_lightning import DDPMLightning
from models.cvae import GraspCVAELightning
from utils.logging_utils import setup_basic_logging, setup_file_logging
from utils.git_utils import get_git_head_hash
from utils.backup_utils import backup_code

os.environ["HYDRA_FULL_ERROR"] = "1"

class TestResultsCallback(pl.Callback):
    def __init__(self, save_path: Path):
        super().__init__()
        self.save_path = save_path
        self.final_test_metrics = {
            "mean_q1": 0.0,
            "mean_pen": 0.0,
            "max_pen": 0.0,
            "mean_valid_q1": 0.0
        }

    def on_test_epoch_end(self, trainer: pl.Trainer, pl_module: pl.LightningModule) -> None:
        all_q1 = []
        all_pen = []
        all_valid_q1 = []
        
        for batch_result in pl_module.metric_results:
            for sample_metrics in batch_result.values():
                all_q1.append(sample_metrics['q1'])
                all_pen.append(sample_metrics['pen'])
                all_valid_q1.append(sample_metrics['valid_q1'])
        
        mean_q1 = sum(all_q1) / len(all_q1)
        mean_pen = sum(all_pen) / len(all_pen)
        max_pen = max(all_pen)
        mean_valid_q1 = sum(all_valid_q1) / len(all_valid_q1)
        
        self.final_test_metrics = {
            "mean_q1": float(mean_q1),
            "mean_pen": float(mean_pen),
            "max_pen": float(max_pen),
            "mean_valid_q1": float(mean_valid_q1)
        }
        
        detailed_results = {
            "summary_metrics": self.final_test_metrics,
            "per_sample_metrics": pl_module.metric_results,
        }
        
        results_file = self.save_path / "test_results.json"
        with open(results_file, 'w') as f:
            json.dump(detailed_results, f, indent=2)
        
        logging.info(f"Saved detailed test results to: {results_file}")
        pl_module.metric_results.clear()

@hydra.main(version_base=None, config_path="config", config_name="config")
def main(cfg: OmegaConf) -> None:
    setup_basic_logging()
    
    if not cfg.get("checkpoint_path") and not cfg.get("train_root"):
        raise ValueError("Must provide either checkpoint_path or train_root for testing")
    
    checkpoint_paths = []
    
    if cfg.get("train_root"):
        train_root = Path(cfg.train_root)
        checkpoint_dir = train_root / "checkpoints"
        checkpoint_paths = sorted(checkpoint_dir.glob("*val_loss*.ckpt"))
        
        if not checkpoint_paths:
            logging.warning(f"No checkpoints containing 'val_loss' found in {checkpoint_dir}")
            return
        
        logging.info(f"Found {len(checkpoint_paths)} checkpoints to test:")
        for path in checkpoint_paths:
            logging.info(f"  - {path.name}")
    else:
        checkpoint_paths = [Path(cfg.checkpoint_path)]
    
    for ckpt_path in checkpoint_paths:
        logging.info(f"\n{'='*50}")
        logging.info(f"Testing checkpoint: {ckpt_path.name}")
        
        original_exp_dir = ckpt_path.parent.parent
        original_config_path = original_exp_dir / "config" / "whole_config.yaml"
        
        if not original_config_path.exists():
            logging.error(f"Original config file not found: {original_config_path}")
            continue
        
        with open(original_config_path, 'r') as f:
            original_cfg = OmegaConf.load(f)
        
        test_cfg = OmegaConf.merge(original_cfg, cfg)
        test_cfg.checkpoint_path = str(ckpt_path)
        
        test_save_dir = original_exp_dir / "test_results" / ckpt_path.stem
        test_save_dir.mkdir(parents=True, exist_ok=True)
        
        setup_file_logging(test_save_dir, mode='test')
        
        git_hash = get_git_head_hash()
        if git_hash:
            logging.info(f"Current git commit: {git_hash}")
        
        config_dir = test_save_dir / "config"
        config_dir.mkdir(exist_ok=True)
        OmegaConf.save(test_cfg, config_dir / "test_config.yaml")
        
        pl.seed_everything(test_cfg.seed, workers=True)
        
        try:
            if test_cfg.model.name == "GraspCVAE":
                model = GraspCVAELightning(test_cfg.model)
            elif test_cfg.model.name == "GraspDiffuser":
                model = DDPMLightning(test_cfg.model)
            else:
                raise ValueError(f"Unknown model name: {test_cfg.model.name}")
            datamodule = SceneLeapDataModule(test_cfg.data)
            
            callbacks = [
                TestResultsCallback(test_save_dir),
                ModelSummary(max_depth=3),
                RichProgressBar(
                    theme=RichProgressBarTheme(
                        description="green_yellow",
                        progress_bar="blue",
                        progress_bar_finished="green",
                        progress_bar_pulse="green1",
                        batch_progress="pink",
                        time="indigo",
                        processing_speed="purple",
                        metrics="yellow",
                    )
                ),
            ]
            
            trainer = pl.Trainer(
                accelerator="gpu",
                devices=1,
                callbacks=callbacks,
                enable_progress_bar=True,
                benchmark=True,
            )
            
            trainer.test(model, datamodule=datamodule, ckpt_path=str(ckpt_path))
            logging.info(f"Test results saved to: {test_save_dir}")
            
        except Exception as e:
            logging.error(f"Testing {ckpt_path.name} failed: {str(e)}", exc_info=True)
            continue
    
    logging.info("All tests completed!")

if __name__ == "__main__":
    main()