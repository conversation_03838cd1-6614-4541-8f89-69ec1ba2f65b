# UNet Decoder Configuration (Cleaned Version)
# 移除了所有未使用的配置项
name: unet
rot_type: ${rot_type}

# Network architecture
d_model: 512
time_embed_mult: 2
nblocks: 4
resblock_dropout: 0.0

# Transformer settings
transformer_num_heads: 8
transformer_dim_head: 64
transformer_dropout: 0.1
transformer_depth: 1
transformer_mult_ff: 2
context_dim: 512

# Backbone settings
backbone:
  name: pointnet2
  use_pooling: false
  layer1:
    npoint: 2048
    radius_list:
    - 0.04
    nsample_list:
    - 64
    mlp_list:
    - 3  # RGB特征是3通道 (xyz坐标会自动处理，这里指定RGB特征的通道数)
    - 64
    - 64
    - 128
  layer2:
    npoint: 1024
    radius_list:
    - 0.1
    nsample_list:
    - 32
    mlp_list:
    - 128
    - 128
    - 128
    - 256
  layer3:
    npoint: 512
    radius_list:
    - 0.2
    nsample_list:
    - 32
    mlp_list:
    - 256
    - 128
    - 128
    - 256
  layer4:
    npoint: 128  # 修改为128个点，作为交叉注意力的Key/Value
    radius_list:
    - 0.3
    nsample_list:
    - 16
    mlp_list:
    - 256
    - 512
    - 512
  use_xyz: true
  normalize_xyz: true

# Other settings
use_position_embedding: false
# fix_num_grasps: ${fix_num_grasps}
# target_num_grasps: ${target_num_grasps}

# Text condition settings
use_text_condition: true
text_dropout_prob: 0.01  # 文本条件随机丢弃概率
use_negative_prompts: ${use_negative_prompts}