object_backbone:
  # name: pointnet2_3sa
  # use_pooling: false
  # layer1:
  #   npoint: 1024
  #   radius_list: [0.02]
  #   nsample_list: [64]
  #   mlp_list: [0, 64, 128]
  # layer2:
  #   npoint: 512
  #   radius_list: [0.05]
  #   nsample_list: [32]
  #   mlp_list: [128, 256, 256]
  # layer3:
  #   npoint: 256
  #   radius_list: [0.1]
  #   nsample_list: [16]
  #   mlp_list:
  #     - 256
  #     - 512
  #     - 512
  # use_xyz: true
  # normalize_xyz: true
  name: pointnet2
  use_pooling: false
  layer1:
    npoint: 2048
    radius_list:
    - 0.04
    nsample_list:
    - 64
    mlp_list:
    - 1
    - 64
    - 64
    - 128
  layer2:
    npoint: 1024
    radius_list:
    - 0.1
    nsample_list:
    - 32
    mlp_list:
    - 128
    - 128
    - 128
    - 256
  layer3:
    npoint: 512
    radius_list:
    - 0.2
    nsample_list:
    - 32
    mlp_list:
    - 256
    - 128
    - 128
    - 256
  layer4:
    npoint: 256
    radius_list:
    - 0.3
    nsample_list:
    - 16
    mlp_list:
    - 256
    - 512
    - 512
  use_xyz: true
  normalize_xyz: true
hand_backbone:
  name: pointnet2_3sa
  use_pooling: false
  layer1:
    npoint: 1024
    radius_list: [0.02]
    nsample_list: [64]
    mlp_list: [0, 64, 128]
  layer2:
    npoint: 512
    radius_list: [0.05]
    nsample_list: [32]
    mlp_list: [128, 256, 256]
  layer3:
    npoint: 256
    radius_list: [0.1]
    nsample_list: [16]
    mlp_list:
      - 256
      - 512
      - 512
  use_xyz: true
  normalize_xyz: true
encoder_dim: 512
condition_dim: 512
hidden_dim: 256
latent_dim: &latent_dim 64
dropout_prob: 0.1