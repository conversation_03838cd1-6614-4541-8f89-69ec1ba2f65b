"""
DDPM Lightning 训练系统
重构后的模块化实现
"""

import pytorch_lightning as pl
import torch
import logging
from typing import Dict, Optional, Any

from models.decoder import build_decoder
from models.loss import GraspLossPose
from models.diffusion import DDPMModel
from models.lightning.training import DDPMTrainingMixin
from models.lightning.validation import DDPMValidationMixin
from models.utils.log_colors import GREEN, ENDC


class DDPMLightning(pl.LightningModule, DDPMTrainingMixin, DDPMValidationMixin):
    """
    DDPM Lightning 训练系统

    职责：
    - 训练/验证/测试流程管理
    - 损失计算和反向传播
    - 优化器和学习率调度器配置
    - 日志记录和指标统计
    - 检查点保存和加载

    不包含：
    - 扩散算法的具体实现
    - 采样过程的数学逻辑
    """

    def __init__(self, cfg):
        super().__init__()
        logging.info(f"{GREEN}Initializing DDPMLightning model{ENDC}")
        self.save_hyperparameters()

        # 构建核心组件
        eps_model = build_decoder(cfg.decoder)
        self.criterion = GraspLossPose(cfg.criterion)

        # 创建扩散模型
        self.diffusion_model = DDPMModel(
            eps_model=eps_model,
            timesteps=cfg.steps,
            schedule_cfg=cfg.schedule_cfg,
            pred_x0=cfg.pred_x0,
            use_cfg=cfg.get('use_cfg', False),
            guidance_scale=cfg.get('guidance_scale', 7.5),
            use_negative_guidance=cfg.get('use_negative_guidance', False),
            negative_guidance_scale=cfg.get('negative_guidance_scale', 1.0)
        )

        # 保存配置
        self.rot_type = cfg.rot_type
        self.loss_weights = cfg.criterion.loss_weights
        self.batch_size = cfg.batch_size
        self.print_freq = cfg.print_freq
        self.use_score = cfg.get('use_score', False)
        self.score_pretrain = cfg.get('score_pretrain', False)
        self.mode = cfg.mode
        self.rand_t_type = cfg.rand_t_type

        # 优化器配置
        self.optimizer_cfg = cfg.optimizer
        self.scheduler = cfg.scheduler

        # 初始化验证输出存储
        self.validation_step_outputs = []

        logging.info(f"{GREEN}DDPMLightning model initialized successfully{ENDC}")
        self._log_model_info()

    def _log_model_info(self):
        """记录模型信息"""
        total_params = sum(p.numel() for p in self.parameters())
        trainable_params = sum(p.numel() for p in self.parameters() if p.requires_grad)

        logging.info(f"{GREEN}Model Information:{ENDC}")
        logging.info(f"{GREEN}  Total parameters: {total_params:,}{ENDC}")
        logging.info(f"{GREEN}  Trainable parameters: {trainable_params:,}{ENDC}")
        logging.info(f"{GREEN}  Diffusion timesteps: {self.diffusion_model.timesteps}{ENDC}")
        logging.info(f"{GREEN}  Use CFG: {self.diffusion_model.use_cfg}{ENDC}")
        if self.diffusion_model.use_cfg:
            logging.info(f"{GREEN}  Guidance scale: {self.diffusion_model.guidance_scale}{ENDC}")

    def forward(self, batch: Dict[str, torch.Tensor]) -> Dict[str, torch.Tensor]:
        """
        前向传播

        Args:
            batch: 输入批次

        Returns:
            output: 模型输出
        """
        # 使用扩散模型进行采样
        samples = self.diffusion_model.sample(batch)

        # 返回最后一个时间步的结果
        pred_x0 = samples[:, 0, -1]  # [k, timesteps, B, ...] -> [B, ...]

        return {
            "pred_pose_norm": pred_x0,
            "samples": samples
        }

    def predict_step(self, batch: Any, batch_idx: int, dataloader_idx: int = 0) -> Any:
        """
        预测步骤

        Args:
            batch: 输入批次
            batch_idx: 批次索引
            dataloader_idx: 数据加载器索引

        Returns:
            predictions: 预测结果
        """
        # 数据预处理
        from utils.hand_helper import process_hand_pose_test
        batch = process_hand_pose_test(batch, rot_type=self.rot_type, mode=self.mode)

        # 前向传播
        output = self.forward(batch)

        return {
            "pred_pose_norm": output["pred_pose_norm"],
            "batch_idx": batch_idx
        }
