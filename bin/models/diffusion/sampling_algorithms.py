"""
DDPM 采样算法实现
包含各种采样方法和CFG支持
"""

import torch
from typing import Dict, Optional
from .utils import prepare_cfg_data, prepare_cfg_data_with_negative, determine_initial_noise_shape


class DDPMSampling:
    """
    DDPM 采样算法实现
    """
    
    def __init__(
        self,
        use_cfg: bool = False,
        guidance_scale: float = 7.5,
        use_negative_guidance: bool = False,
        negative_guidance_scale: float = 1.0
    ):
        """
        初始化采样器
        
        Args:
            use_cfg: 是否使用分类器自由引导
            guidance_scale: 引导强度
            use_negative_guidance: 是否使用负向引导
            negative_guidance_scale: 负向引导强度
        """
        self.use_cfg = use_cfg
        self.guidance_scale = guidance_scale
        self.use_negative_guidance = use_negative_guidance
        self.negative_guidance_scale = negative_guidance_scale
    
    @torch.no_grad()
    def p_sample(self, core, x_t: torch.Tensor, t: int, data: Dict) -> torch.Tensor:
        """
        单步采样：从 x_t 采样得到 x_{t-1}
        
        Args:
            core: DDPMCore 实例
            x_t: 当前时间步的噪声数据
            t: 当前时间步
            data: 条件数据
            
        Returns:
            pred_x: 下一时间步的数据
        """
        B, *_ = x_t.shape
        batch_timestep = torch.full((B, ), t, device=core.device, dtype=torch.long)

        if self.use_cfg and not core.training:
            model_mean, model_variance, model_log_variance = self.p_mean_variance_cfg(core, x_t, batch_timestep, data)
        else:
            model_mean, model_variance, model_log_variance = core.p_mean_variance(x_t, batch_timestep, data)
        
        noise = torch.randn_like(x_t) if t > 0 else 0.
        pred_x = model_mean + (0.5 * model_log_variance).exp() * noise
        return pred_x
    
    @torch.no_grad()
    def p_sample_loop(self, core, data: Dict) -> torch.Tensor:
        """
        采样循环：从噪声逐步去噪生成干净数据
        支持单抓取和多抓取格式，实现 One-shot Parallel Decoding
        
        Args:
            core: DDPMCore 实例
            data: 条件数据
            
        Returns:
            samples: 采样结果 [k, B, ...] 或 [k, B, num_grasps, ...]
        """
        device = next(core.parameters()).device
        noise_shape = determine_initial_noise_shape(data)
        
        # 初始化噪声
        x = torch.randn(noise_shape, device=device)
        
        # 存储采样过程
        samples = []
        
        # 逐步去噪
        for i in reversed(range(core.timesteps)):
            x = self.p_sample(core, x, i, data)
            samples.append(x.clone())
        
        # 返回格式: [timesteps, B, ...] 或 [timesteps, B, num_grasps, ...]
        return torch.stack(samples, dim=0)
    
    @torch.no_grad()
    def sample(self, core, data: Dict, k: int = 1, **kwargs) -> torch.Tensor:
        """
        完整采样接口
        
        Args:
            core: DDPMCore 实例
            data: 条件数据
            k: 采样次数
            
        Returns:
            samples: 采样结果
        """
        all_samples = []
        
        for _ in range(k):
            samples = self.p_sample_loop(core, data)
            all_samples.append(samples)
        
        # 返回格式: [k, timesteps, B, ...] 或 [k, timesteps, B, num_grasps, ...]
        return torch.stack(all_samples, dim=0)
    
    def p_mean_variance_cfg(self, core, x_t: torch.Tensor, t: torch.Tensor, data: Dict) -> tuple:
        """
        使用CFG的后验分布计算
        
        Args:
            core: DDPMCore 实例
            x_t: 当前时间步的噪声数据
            t: 时间步
            data: 条件数据
            
        Returns:
            model_mean: 后验均值
            model_variance: 后验方差
            model_log_variance: 后验对数方差
        """
        if x_t.dim() == 2:
            return self._p_mean_variance_cfg_single(core, x_t, t, data)
        elif x_t.dim() == 3:
            return self._p_mean_variance_cfg_multi(core, x_t, t, data)
        else:
            raise ValueError(f"Unsupported input dimension: {x_t.dim()}")
    
    def _p_mean_variance_cfg_single(self, core, x_t: torch.Tensor, t: torch.Tensor, data: Dict) -> tuple:
        """单抓取格式的CFG后验分布计算"""
        B, *x_shape = x_t.shape
        
        # 准备CFG数据
        cfg_data = self._prepare_cfg_data(data, B)
        
        # 扩展输入
        x_t_cfg = torch.cat([x_t, x_t], dim=0)
        t_cfg = torch.cat([t, t], dim=0)
        
        # 模型预测
        pred_noise, pred_x0 = core.model_predict(x_t_cfg, t_cfg, cfg_data)
        
        # 应用CFG
        pred_noise, pred_x0 = self._apply_cfg_guidance(pred_noise, pred_x0)
        
        # 计算后验分布
        coef1 = core.posterior_mean_coef1[t].reshape(B, *((1, ) * len(x_shape)))
        coef2 = core.posterior_mean_coef2[t].reshape(B, *((1, ) * len(x_shape)))
        
        model_mean = coef1 * pred_x0 + coef2 * x_t
        
        posterior_variance = core.posterior_variance[t].reshape(B, *((1, ) * len(x_shape)))
        posterior_log_variance = core.posterior_log_variance_clipped[t].reshape(B, *((1, ) * len(x_shape)))
        
        return model_mean, posterior_variance, posterior_log_variance
    
    def _p_mean_variance_cfg_multi(self, core, x_t: torch.Tensor, t: torch.Tensor, data: Dict) -> tuple:
        """多抓取格式的CFG后验分布计算"""
        B, num_grasps, pose_dim = x_t.shape
        
        # 准备CFG数据
        cfg_data = self._prepare_cfg_data(data, B)
        
        # 扩展输入
        x_t_cfg = torch.cat([x_t, x_t], dim=0)  # [2B, num_grasps, pose_dim]
        t_cfg = torch.cat([t, t], dim=0)  # [2B]
        
        # 模型预测
        pred_noise, pred_x0 = core.model_predict(x_t_cfg, t_cfg, cfg_data)
        
        # 应用CFG
        pred_noise, pred_x0 = self._apply_cfg_guidance(pred_noise, pred_x0)
        
        # 计算后验分布
        from .utils import expand_time_for_multi_grasp
        t_expanded = expand_time_for_multi_grasp(t, x_t.shape)
        coef1 = core.posterior_mean_coef1[t_expanded].unsqueeze(-1)
        coef2 = core.posterior_mean_coef2[t_expanded].unsqueeze(-1)
        
        pred_x0_flat = pred_x0.reshape(-1, pose_dim)
        x_t_flat = x_t.reshape(-1, pose_dim)
        
        model_mean_flat = coef1 * pred_x0_flat + coef2 * x_t_flat
        model_mean = model_mean_flat.reshape(B, num_grasps, pose_dim)
        
        posterior_variance = core.posterior_variance[t_expanded].unsqueeze(-1)
        posterior_log_variance = core.posterior_log_variance_clipped[t_expanded].unsqueeze(-1)
        
        return model_mean, posterior_variance, posterior_log_variance
    
    def _prepare_cfg_data(self, data: Dict, B: int) -> Dict:
        """准备CFG数据"""
        if self.use_negative_guidance and 'negative_data' in data:
            return prepare_cfg_data_with_negative(data, B)
        else:
            return prepare_cfg_data(data, B)
    
    def _apply_cfg_guidance(self, pred_noise: torch.Tensor, pred_x0: torch.Tensor) -> tuple:
        """应用CFG引导"""
        if self.use_negative_guidance:
            # 三路CFG：正向 + 负向 + 无条件
            pred_noise_pos, pred_noise_neg, pred_noise_uncond = pred_noise.chunk(3, dim=0)
            pred_x0_pos, pred_x0_neg, pred_x0_uncond = pred_x0.chunk(3, dim=0)

            pred_noise = pred_noise_uncond + self.guidance_scale * (pred_noise_pos - pred_noise_uncond) + \
                        self.negative_guidance_scale * (pred_noise_uncond - pred_noise_neg)
            pred_x0 = pred_x0_uncond + self.guidance_scale * (pred_x0_pos - pred_x0_uncond) + \
                     self.negative_guidance_scale * (pred_x0_uncond - pred_x0_neg)
        else:
            # 标准CFG：条件 + 无条件
            pred_noise_cond, pred_noise_uncond = pred_noise.chunk(2, dim=0)
            pred_x0_cond, pred_x0_uncond = pred_x0.chunk(2, dim=0)

            pred_noise = pred_noise_uncond + self.guidance_scale * (pred_noise_cond - pred_noise_uncond)
            pred_x0 = pred_x0_uncond + self.guidance_scale * (pred_x0_cond - pred_x0_uncond)
        
        return pred_noise, pred_x0
