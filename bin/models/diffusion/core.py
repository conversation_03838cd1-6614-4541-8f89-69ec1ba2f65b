"""
DDPM 扩散模型核心算法
包含前向扩散、模型预测和后验分布计算
"""

import torch
import torch.nn as nn
from typing import Dict, Tuple
from models.utils.diffusion_utils import make_schedule_ddpm
from .utils import handle_multi_grasp_operation, expand_time_for_multi_grasp


class DDPMCore(nn.Module):
    """
    DDPM 核心扩散算法实现
    专注于扩散过程的数学计算
    """
    
    def __init__(
        self,
        eps_model: nn.Module,
        timesteps: int,
        schedule_cfg: Dict,
        pred_x0: bool = False
    ):
        """
        初始化扩散核心
        
        Args:
            eps_model: 噪声预测网络
            timesteps: 扩散步数
            schedule_cfg: 扩散调度配置
            pred_x0: 是否直接预测干净数据
        """
        super().__init__()
        
        self.eps_model = eps_model
        self.timesteps = timesteps
        self.pred_x0 = pred_x0
        
        # 注册扩散参数
        # 处理配置格式兼容性
        if 'beta' in schedule_cfg and 'beta_schedule' in schedule_cfg:
            # 新格式：直接传递
            schedule_params = make_schedule_ddpm(timesteps, **schedule_cfg)
        else:
            # 旧格式：转换参数名
            beta_start = schedule_cfg.get('beta_start', 0.0001)
            beta_end = schedule_cfg.get('beta_end', 0.02)
            beta_schedule = schedule_cfg.get('schedule', 'linear')
            schedule_params = make_schedule_ddpm(
                timesteps,
                beta=[beta_start, beta_end],
                beta_schedule=beta_schedule
            )

        for k, v in schedule_params.items():
            self.register_buffer(k, v)
    
    def q_sample(self, x0: torch.Tensor, t: torch.Tensor, noise: torch.Tensor) -> torch.Tensor:
        """
        前向扩散过程：给干净数据加噪
        支持单抓取格式 [B, pose_dim] 和多抓取格式 [B, num_grasps, pose_dim]
        
        Args:
            x0: 干净数据
            t: 时间步
            noise: 噪声
            
        Returns:
            x_t: 加噪后的数据
        """
        def single_q_sample(x, time_step):
            B, *x_shape = x.shape
            sqrt_alphas_cumprod_t = self.sqrt_alphas_cumprod[time_step].reshape(B, *((1, ) * len(x_shape)))
            sqrt_one_minus_alphas_cumprod_t = self.sqrt_one_minus_alphas_cumprod[time_step].reshape(B, *((1, ) * len(x_shape)))
            return sqrt_alphas_cumprod_t * x + sqrt_one_minus_alphas_cumprod_t * noise.reshape(x.shape)
        
        def multi_q_sample(x, time_step):
            B, num_grasps, pose_dim = x.shape
            t_expanded = expand_time_for_multi_grasp(time_step, x.shape)
            
            x_flat = x.reshape(-1, pose_dim)
            noise_flat = noise.reshape(-1, pose_dim)
            
            sqrt_alphas_cumprod_t = self.sqrt_alphas_cumprod[t_expanded].unsqueeze(-1)
            sqrt_one_minus_alphas_cumprod_t = self.sqrt_one_minus_alphas_cumprod[t_expanded].unsqueeze(-1)
            
            x_t_flat = sqrt_alphas_cumprod_t * x_flat + sqrt_one_minus_alphas_cumprod_t * noise_flat
            return x_t_flat.reshape(B, num_grasps, pose_dim)
        
        return handle_multi_grasp_operation(x0, t, single_q_sample, multi_q_sample)
    
    def model_predict(self, x_t: torch.Tensor, t: torch.Tensor, data: Dict) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        模型预测：根据噪声数据预测噪声或干净数据
        
        Args:
            x_t: 噪声数据
            t: 时间步
            data: 条件数据
            
        Returns:
            pred_noise: 预测噪声
            pred_x0: 预测干净数据
        """
        if x_t.dim() == 2:
            return self._model_predict_single(x_t, t, data)
        elif x_t.dim() == 3:
            return self._model_predict_multi(x_t, t, data)
        else:
            raise ValueError(f"Unsupported input dimension: {x_t.dim()}. Expected 2 or 3.")
    
    def _model_predict_single(self, x_t: torch.Tensor, t: torch.Tensor, data: Dict) -> Tuple[torch.Tensor, torch.Tensor]:
        """单抓取格式的模型预测"""
        B, *x_shape = x_t.shape
        
        if self.pred_x0:
            pred_x0 = self.eps_model(x_t, t, data)
            pred_noise = (self.sqrt_recip_alphas_cumprod[t].reshape(B, *((1, ) * len(x_shape))) * x_t - pred_x0) \
                        / self.sqrt_recipm1_alphas_cumprod[t].reshape(B, *((1, ) * len(x_shape)))
        else:
            pred_noise = self.eps_model(x_t, t, data)
            pred_x0 = self.sqrt_recip_alphas_cumprod[t].reshape(B, *((1, ) * len(x_shape))) * x_t - \
                        self.sqrt_recipm1_alphas_cumprod[t].reshape(B, *((1, ) * len(x_shape))) * pred_noise
        
        return pred_noise, pred_x0
    
    def _model_predict_multi(self, x_t: torch.Tensor, t: torch.Tensor, data: Dict) -> Tuple[torch.Tensor, torch.Tensor]:
        """多抓取格式的模型预测"""
        B, num_grasps, pose_dim = x_t.shape
        
        # 重塑为单抓取格式进行预测
        x_t_flat = x_t.reshape(-1, pose_dim)
        t_expanded = expand_time_for_multi_grasp(t, x_t.shape)
        
        # 准备数据
        data_expanded = self._expand_data_for_multi_grasp(data, B, num_grasps)
        
        if self.pred_x0:
            pred_x0_flat = self.eps_model(x_t_flat, t_expanded, data_expanded)
            pred_noise_flat = (self.sqrt_recip_alphas_cumprod[t_expanded].unsqueeze(-1) * x_t_flat - pred_x0_flat) \
                            / self.sqrt_recipm1_alphas_cumprod[t_expanded].unsqueeze(-1)
        else:
            pred_noise_flat = self.eps_model(x_t_flat, t_expanded, data_expanded)
            pred_x0_flat = self.sqrt_recip_alphas_cumprod[t_expanded].unsqueeze(-1) * x_t_flat - \
                          self.sqrt_recipm1_alphas_cumprod[t_expanded].unsqueeze(-1) * pred_noise_flat
        
        # 重塑回多抓取格式
        pred_noise = pred_noise_flat.reshape(B, num_grasps, pose_dim)
        pred_x0 = pred_x0_flat.reshape(B, num_grasps, pose_dim)
        
        return pred_noise, pred_x0
    
    def _expand_data_for_multi_grasp(self, data: Dict, B: int, num_grasps: int) -> Dict:
        """为多抓取格式扩展数据"""
        expanded_data = {}
        
        for key, value in data.items():
            if isinstance(value, torch.Tensor):
                if key in ['norm_pose', 'hand_pose', 'hand_pose_norm']:
                    # 姿态数据已经是多抓取格式，直接flatten
                    if value.dim() == 3:
                        expanded_data[key] = value.reshape(-1, value.shape[-1])
                    else:
                        # 单抓取格式，需要扩展
                        expanded_data[key] = value.unsqueeze(1).expand(B, num_grasps, -1).reshape(-1, value.shape[-1])
                else:
                    # 其他数据（图像、点云等），每个抓取都使用相同的数据
                    if value.dim() >= 2:
                        expanded_data[key] = value.unsqueeze(1).expand(-1, num_grasps, *[-1] * (value.dim() - 1)).reshape(-1, *value.shape[1:])
                    else:
                        expanded_data[key] = value.unsqueeze(1).expand(-1, num_grasps).reshape(-1)
            else:
                expanded_data[key] = value
        
        return expanded_data
    
    def p_mean_variance(self, x_t: torch.Tensor, t: torch.Tensor, data: Dict) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """
        计算后验分布的均值和方差
        
        Args:
            x_t: 当前时间步的噪声数据
            t: 时间步
            data: 条件数据
            
        Returns:
            model_mean: 后验均值
            model_variance: 后验方差
            model_log_variance: 后验对数方差
        """
        pred_noise, pred_x0 = self.model_predict(x_t, t, data)
        
        if x_t.dim() == 2:
            return self._p_mean_variance_single(x_t, t, pred_x0)
        elif x_t.dim() == 3:
            return self._p_mean_variance_multi(x_t, t, pred_x0)
        else:
            raise ValueError(f"Unsupported input dimension: {x_t.dim()}")
    
    def _p_mean_variance_single(self, x_t: torch.Tensor, t: torch.Tensor, pred_x0: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """单抓取格式的后验分布计算"""
        B, *x_shape = x_t.shape
        
        coef1 = self.posterior_mean_coef1[t].reshape(B, *((1, ) * len(x_shape)))
        coef2 = self.posterior_mean_coef2[t].reshape(B, *((1, ) * len(x_shape)))
        
        model_mean = coef1 * pred_x0 + coef2 * x_t
        
        posterior_variance = self.posterior_variance[t].reshape(B, *((1, ) * len(x_shape)))
        posterior_log_variance = self.posterior_log_variance_clipped[t].reshape(B, *((1, ) * len(x_shape)))
        
        return model_mean, posterior_variance, posterior_log_variance
    
    def _p_mean_variance_multi(self, x_t: torch.Tensor, t: torch.Tensor, pred_x0: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """多抓取格式的后验分布计算"""
        B, num_grasps, pose_dim = x_t.shape
        
        t_expanded = expand_time_for_multi_grasp(t, x_t.shape)
        coef1 = self.posterior_mean_coef1[t_expanded].unsqueeze(-1)
        coef2 = self.posterior_mean_coef2[t_expanded].unsqueeze(-1)
        
        pred_x0_flat = pred_x0.reshape(-1, pose_dim)
        x_t_flat = x_t.reshape(-1, pose_dim)
        
        model_mean_flat = coef1 * pred_x0_flat + coef2 * x_t_flat
        model_mean = model_mean_flat.reshape(B, num_grasps, pose_dim)
        
        posterior_variance = self.posterior_variance[t_expanded].unsqueeze(-1)
        posterior_log_variance = self.posterior_log_variance_clipped[t_expanded].unsqueeze(-1)
        
        return model_mean, posterior_variance, posterior_log_variance
