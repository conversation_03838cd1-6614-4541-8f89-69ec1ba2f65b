"""
扩散模型工具函数
包含维度处理、数据准备等通用工具
"""

import torch
from typing import Dict, Optional, Tuple, Callable, Any


def handle_multi_grasp_operation(
    tensor: torch.Tensor, 
    t: torch.Tensor, 
    single_op: Callable, 
    multi_op: Callable = None
) -> torch.Tensor:
    """
    统一处理单抓取和多抓取格式的tensor操作
    
    Args:
        tensor: 输入tensor
        t: 时间步
        single_op: 单抓取格式的操作函数
        multi_op: 多抓取格式的操作函数（可选，默认使用single_op）
        
    Returns:
        处理后的tensor
    """
    if tensor.dim() == 2:
        return single_op(tensor, t)
    elif tensor.dim() == 3:
        if multi_op is not None:
            return multi_op(tensor, t)
        else:
            # 使用默认的多抓取处理：flatten -> 操作 -> reshape
            B, num_grasps, pose_dim = tensor.shape
            t_expanded = t.unsqueeze(1).expand(B, num_grasps).reshape(-1)
            tensor_flat = tensor.reshape(-1, pose_dim)
            result_flat = single_op(tensor_flat, t_expanded)
            return result_flat.reshape(B, num_grasps, pose_dim)
    else:
        raise ValueError(f"Unsupported tensor dimension: {tensor.dim()}. Expected 2 or 3.")


def expand_time_for_multi_grasp(t: torch.Tensor, shape: Tuple[int, ...]) -> torch.Tensor:
    """
    为多抓取格式扩展时间步
    
    Args:
        t: 时间步 [B]
        shape: tensor形状
        
    Returns:
        扩展后的时间步
    """
    if len(shape) == 2:
        return t
    elif len(shape) == 3:
        B, num_grasps, _ = shape
        return t.unsqueeze(1).expand(B, num_grasps).reshape(-1)
    else:
        raise ValueError(f"Unsupported shape: {shape}")


def prepare_cfg_data(data: Dict, B: int) -> Dict:
    """
    为CFG准备数据，支持多抓取格式
    
    Args:
        data: 原始数据字典
        B: 批次大小
        
    Returns:
        cfg_data: CFG格式的数据字典
    """
    cfg_data = {}

    for key, value in data.items():
        if key in ['rgb', 'depth', 'seg', 'xyz', 'normal', 'obj_pc', 'obj_pc_norm']:
            # 图像和点云数据：复制两次用于条件和无条件
            cfg_data[key] = torch.cat([value, value], dim=0)
        
        elif key in ['norm_pose', 'hand_pose', 'hand_pose_norm']:
            # 姿态数据：支持多维度
            cfg_data[key] = torch.cat([value, value], dim=0)
        
        elif key in ['condition_mask']:
            # 条件掩码：第一份为True（条件），第二份为False（无条件）
            if isinstance(value, torch.Tensor):
                uncond_mask = torch.zeros_like(value)
                cfg_data[key] = torch.cat([value, uncond_mask], dim=0)
            else:
                # 如果是布尔值，转换为tensor
                device = getattr(value, 'device', 'cpu')
                cond_mask = torch.ones(B, dtype=torch.bool, device=device)
                uncond_mask = torch.zeros(B, dtype=torch.bool, device=device)
                cfg_data[key] = torch.cat([cond_mask, uncond_mask], dim=0)
        
        else:
            # 其他数据：直接复制
            if isinstance(value, torch.Tensor):
                cfg_data[key] = torch.cat([value, value], dim=0)
            else:
                cfg_data[key] = value

    return cfg_data


def prepare_cfg_data_with_negative(data: Dict, B: int) -> Dict:
    """
    为带负向引导的CFG准备数据
    
    Args:
        data: 原始数据字典，应包含 'negative_data' 键
        B: 批次大小
        
    Returns:
        cfg_data: 三路CFG格式的数据字典（正向条件 + 负向条件 + 无条件）
    """
    cfg_data = {}
    negative_data = data.get('negative_data', {})

    for key, value in data.items():
        if key == 'negative_data':
            continue
            
        if key in ['rgb', 'depth', 'seg', 'xyz', 'normal', 'obj_pc', 'obj_pc_norm', 
                   'norm_pose', 'hand_pose', 'hand_pose_norm']:
            # 数据：正向条件 + 负向条件 + 无条件
            neg_value = negative_data.get(key, value)
            cfg_data[key] = torch.cat([value, neg_value, value], dim=0)
        
        elif key in ['condition_mask']:
            # 条件掩码：正向True + 负向True + 无条件False
            if isinstance(value, torch.Tensor):
                neg_mask = negative_data.get(key, value)
                uncond_mask = torch.zeros_like(value)
                cfg_data[key] = torch.cat([value, neg_mask, uncond_mask], dim=0)
            else:
                device = getattr(value, 'device', 'cpu')
                cond_mask = torch.ones(B, dtype=torch.bool, device=device)
                neg_mask = torch.ones(B, dtype=torch.bool, device=device)
                uncond_mask = torch.zeros(B, dtype=torch.bool, device=device)
                cfg_data[key] = torch.cat([cond_mask, neg_mask, uncond_mask], dim=0)
        
        else:
            # 其他数据
            if isinstance(value, torch.Tensor):
                neg_value = negative_data.get(key, value)
                cfg_data[key] = torch.cat([value, neg_value, value], dim=0)
            else:
                cfg_data[key] = value

    return cfg_data


def determine_initial_noise_shape(data: Dict) -> tuple:
    """
    根据数据格式确定初始噪声形状
    
    Args:
        data: 数据字典
        
    Returns:
        noise_shape: 噪声形状元组
    """
    if 'norm_pose' in data:
        pose_shape = data['norm_pose'].shape
        if len(pose_shape) in [2, 3]:
            return pose_shape
        else:
            raise ValueError(f"Unsupported pose shape: {pose_shape}")
    else:
        raise ValueError("No 'norm_pose' found in data")
