"""
完整的 DDPM 扩散模型
组合核心算法和采样功能
"""

import torch
import torch.nn as nn
from typing import Dict, Optional, Tuple, Any
from .core import DDPMCore
from .sampling_algorithms import DDPMSampling


class DDPMModel(nn.Module):
    """
    完整的 DDPM 扩散模型
    
    职责：
    - 扩散过程的数学实现（前向加噪、反向去噪）
    - 扩散参数管理（alpha、beta 调度）
    - 采样算法实现（DDPM、CFG 等）
    - 条件编码处理
    
    不包含：
    - 训练循环逻辑
    - 损失计算和优化
    - 日志记录和指标统计
    - 检查点管理
    """
    
    def __init__(
        self,
        eps_model: nn.Module,
        timesteps: int,
        schedule_cfg: Dict,
        pred_x0: bool = False,
        use_cfg: bool = False,
        guidance_scale: float = 7.5,
        use_negative_guidance: bool = False,
        negative_guidance_scale: float = 1.0
    ):
        """
        初始化 DDPM 模型
        
        Args:
            eps_model: 噪声预测网络（UNet 等）
            timesteps: 扩散步数
            schedule_cfg: 扩散调度配置
            pred_x0: 是否直接预测干净数据
            use_cfg: 是否使用分类器自由引导
            guidance_scale: 引导强度
            use_negative_guidance: 是否使用负向引导
            negative_guidance_scale: 负向引导强度
        """
        super().__init__()
        
        # 核心扩散算法
        self.core = DDPMCore(
            eps_model=eps_model,
            timesteps=timesteps,
            schedule_cfg=schedule_cfg,
            pred_x0=pred_x0
        )
        
        # 采样算法
        self.sampler = DDPMSampling(
            use_cfg=use_cfg,
            guidance_scale=guidance_scale,
            use_negative_guidance=use_negative_guidance,
            negative_guidance_scale=negative_guidance_scale
        )
        
        # 保存配置
        self.timesteps = timesteps
        self.use_cfg = use_cfg
        self.guidance_scale = guidance_scale
    
    @property
    def device(self):
        """获取模型设备"""
        return next(self.parameters()).device
    
    def q_sample(self, x0: torch.Tensor, t: torch.Tensor, noise: torch.Tensor) -> torch.Tensor:
        """前向扩散过程：给干净数据加噪"""
        return self.core.q_sample(x0, t, noise)
    
    def model_predict(self, x_t: torch.Tensor, t: torch.Tensor, data: Dict) -> Tuple[torch.Tensor, torch.Tensor]:
        """模型预测：返回预测噪声和预测干净数据"""
        return self.core.model_predict(x_t, t, data)
    
    def p_mean_variance(self, x_t: torch.Tensor, t: torch.Tensor, data: Dict) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """计算后验分布的均值和方差"""
        return self.core.p_mean_variance(x_t, t, data)
    
    @torch.no_grad()
    def sample(self, data: Dict, k: int = 1, **kwargs) -> torch.Tensor:
        """完整采样过程：从噪声生成干净数据"""
        return self.sampler.sample(self.core, data, k, **kwargs)
    
    @torch.no_grad()
    def p_sample(self, x_t: torch.Tensor, t: int, data: Dict) -> torch.Tensor:
        """单步采样：从 x_t 采样得到 x_{t-1}"""
        return self.sampler.p_sample(self.core, x_t, t, data)
    
    @torch.no_grad()
    def p_sample_loop(self, data: Dict) -> torch.Tensor:
        """采样循环：从噪声逐步去噪生成干净数据"""
        return self.sampler.p_sample_loop(self.core, data)
    
    def compute_training_loss(self, batch: Dict, criterion) -> Dict[str, torch.Tensor]:
        """
        计算训练损失
        
        Args:
            batch: 训练批次数据
            criterion: 损失函数
            
        Returns:
            loss_dict: 损失字典
        """
        device = self.device
        
        # 获取目标数据
        if 'norm_pose' in batch:
            x0 = batch['norm_pose'].to(device)
        else:
            raise ValueError("No 'norm_pose' found in batch")
        
        # 采样时间步
        B = x0.shape[0]
        t = torch.randint(0, self.timesteps, (B,), device=device).long()
        
        # 生成噪声
        noise = torch.randn_like(x0)
        
        # 前向扩散
        x_t = self.q_sample(x0, t, noise)
        
        # 模型预测
        pred_noise, pred_x0 = self.model_predict(x_t, t, batch)
        
        # 构建预测字典
        pred_dict = {
            "pred_pose_norm": pred_x0,
            "pred_noise": pred_noise,
            "noise": noise
        }
        
        # 添加其他预测结果
        if pred_x0.dim() == 3:
            # 多抓取格式
            pred_dict.update({
                "qpos_norm": pred_x0[..., 3:19],
                "translation_norm": pred_x0[..., :3],
                "rotation": pred_x0[..., 19:],
            })
        else:
            # 单抓取格式
            pred_dict.update({
                "qpos_norm": pred_x0[..., 3:19],
                "translation_norm": pred_x0[..., :3],
                "rotation": pred_x0[..., 19:],
            })
        
        # 计算损失
        loss_dict = criterion(pred_dict, batch, mode='train')
        
        return loss_dict
