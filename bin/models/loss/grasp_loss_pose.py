import logging
import os
from statistics import mean
from typing import Dict, List, Tuple

import numpy as np
import torch
from torch import nn
import torch.nn.functional as F
from torch.functional import Tensor

import pytorch3d.structures
import pytorch3d.ops
import pytorch3d.transforms
from pytorch3d.loss import chamfer_distance

from utils.hand_model import HandModel, HandModelType
from utils.hand_helper import denorm_hand_pose_robust
from utils.evaluate_utils import cal_q1, cal_pen
from .matcher import Matcher


class GraspLossPose(nn.Module):
    def __init__(self, loss_cfg):
        super().__init__()

        # Dynamically determine correct device for distributed training
        device = self._get_correct_device(loss_cfg.device)

        self.hand_model = HandModel(HandModelType.LEAP, loss_cfg.hand_model.n_surface_points, loss_cfg.rot_type, device)
        self.loss_weights = {k: v for k, v in loss_cfg.loss_weights.items() if v > 0}  # Only keep losses with weight > 0
        self.matcher = Matcher(weight_dict=loss_cfg.cost_weights, rot_type=loss_cfg.rot_type)
        self.rot_type = loss_cfg.rot_type
        self.q1_cfg = loss_cfg.q1
        self.scale = 0.1
        self.mode = loss_cfg.mode

        # Multi-grasp configuration
        self.multi_grasp_cfg = getattr(loss_cfg, 'multi_grasp', None)
        if self.multi_grasp_cfg:
            self.loss_aggregation = getattr(self.multi_grasp_cfg, 'loss_aggregation', 'mean')
            self.use_consistency_loss = getattr(self.multi_grasp_cfg, 'use_consistency_loss', False)
            self.consistency_loss_weight = getattr(self.multi_grasp_cfg, 'consistency_loss_weight', 0.1)
            self.diversity_loss_weight = getattr(self.multi_grasp_cfg, 'diversity_loss_weight', 0.05)
        else:
            self.loss_aggregation = 'mean'
            self.use_consistency_loss = False
            self.consistency_loss_weight = 0.1
            self.diversity_loss_weight = 0.05

        # Negative loss weight for compatibility with diffuser_lightning.py
        # self.neg_loss_weight = getattr(loss_cfg, 'neg_loss_weight', 1.0)

        # Configure hand model parameters
        self._configure_hand_model_kwargs()

    def _get_device(self, *tensors):
        """
        Automatically infer device, supporting distributed training
        Prioritize inference from input tensors, fallback to hand_model device
        """
        for tensor in tensors:
            if tensor is not None and hasattr(tensor, 'device'):
                return tensor.device

        # Infer device from hand_model
        if hasattr(self.hand_model, 'device'):
            return self.hand_model.device

        # Infer device from hand_model parameters
        try:
            return next(self.hand_model.parameters()).device
        except (StopIteration, AttributeError):
            pass

        # Final fallback to CPU
        return torch.device('cpu')

    def _get_correct_device(self, config_device):
        """
        Determine correct device for distributed training
        """
        import os
        import torch

        # Check if in distributed training environment
        if 'LOCAL_RANK' in os.environ:
            local_rank = int(os.environ['LOCAL_RANK'])
            if torch.cuda.is_available():
                device = f'cuda:{local_rank}'
                logging.info(f"GraspLossPose: Using device {device} for LOCAL_RANK {local_rank}")
                return device
        elif 'RANK' in os.environ:
            # If only RANK but no LOCAL_RANK, try using RANK
            rank = int(os.environ['RANK'])
            if torch.cuda.is_available():
                # Assume same number of GPUs per node
                local_rank = rank % torch.cuda.device_count()
                device = f'cuda:{local_rank}'
                logging.info(f"GraspLossPose: Using device {device} for RANK {rank}")
                return device

        # Non-distributed environment or unable to determine rank, use config device
        logging.info(f"GraspLossPose: Using config device {config_device}")
        return config_device

    def to(self, device):
        """
        Override to method to ensure hand_model also moves to correct device
        """
        result = super().to(device)

        # Synchronously update hand_model device
        if hasattr(self, 'hand_model') and self.hand_model is not None:
            # Update hand_model device attribute
            self.hand_model.device = device

            # Move all tensors in hand_model to new device
            if hasattr(self.hand_model, 'chain') and self.hand_model.chain is not None:
                self.hand_model.chain = self.hand_model.chain.to(device=device)

            # Move mesh data to new device
            if hasattr(self.hand_model, 'mesh'):
                for link_name, mesh_data in self.hand_model.mesh.items():
                    for key, tensor in mesh_data.items():
                        if isinstance(tensor, torch.Tensor):
                            mesh_data[key] = tensor.to(device)

            # Move other tensor attributes to new device
            for attr_name in ['joints_upper', 'joints_lower']:
                if hasattr(self.hand_model, attr_name):
                    attr_value = getattr(self.hand_model, attr_name)
                    if isinstance(attr_value, torch.Tensor):
                        setattr(self.hand_model, attr_name, attr_value.to(device))

            logging.info(f"GraspLossPose: Moved hand_model to device {device}")

        return result

    def _configure_hand_model_kwargs(self):
        """Configure hand model parameters"""
        # Configure hand model parameters for train/val phase based on active losses
        self.train_val_hand_model_kwargs = {
            "with_meshes": False,
            "with_surface_points": False,
            "with_contact_candidates": False,
            "with_penetration": False,
            "with_penetration_keypoints": False,
            "with_distance": False,
            "with_fingertip_keypoints": False,
        }
        
        if "hand_chamfer" in self.loss_weights:
            self.train_val_hand_model_kwargs["with_surface_points"] = True
        if "obj_penetration" in self.loss_weights:
            self.train_val_hand_model_kwargs["with_penetration"] = True
            self.train_val_hand_model_kwargs["with_penetration_keypoints"] = True
        if "self_penetration" in self.loss_weights:
            self.train_val_hand_model_kwargs["with_penetration_keypoints"] = True
        if "distance" in self.loss_weights:
            self.train_val_hand_model_kwargs["with_contact_candidates"] = True
        if "cmap" in self.loss_weights:
            self.train_val_hand_model_kwargs["with_distance"] = True

        logging.info(f"GraspLossPose initialized. train_val_hand_model_kwargs: {self.train_val_hand_model_kwargs}")

        # Configure hand model parameters for test/inference phase
        self.test_infer_hand_model_kwargs = {
            "with_meshes": True,
            "with_surface_points": True,
            "with_contact_candidates": True,
            "with_penetration": False,
            "with_penetration_keypoints": False,
            "with_distance": False,
            "with_fingertip_keypoints": False,
        }

    def forward(self, pred_dict, batch, mode='train'):
        if mode == 'train':
            return self._forward_train(pred_dict, batch)
        elif mode == 'val':
            return self._forward_val(pred_dict, batch)
        elif mode == 'test':
            return self._forward_test(pred_dict, batch)
        else:
            raise ValueError(f"Unknown mode: {mode}")

    def _forward_train(self, pred_dict, batch):
        outputs, targets = self.get_hand_model_pose(pred_dict, batch)
        outputs, targets = self._prepare_hand_data(outputs, targets, self.train_val_hand_model_kwargs)
        return self._calculate_losses(outputs, targets)

    def _forward_val(self, pred_dict, batch):
        outputs = self.get_hand_model_pose_test(pred_dict)
        
        assignments = self.matcher(outputs, batch)
        matched_preds, matched_targets = self.get_matched_by_assignment(outputs, batch, assignments)
        
        outputs['matched'] = matched_preds
        batch['matched'] = matched_targets
        batch['matched']['scene_pc'] = batch['scene_pc']
        outputs['matched']['neg_pred'] = batch['neg_pred']
        outputs['matched']['neg_text_features'] = batch['neg_text_features']
        
        outputs, targets = self._prepare_hand_data(outputs, batch, self.train_val_hand_model_kwargs)
        return self._calculate_losses(outputs, targets)

    def _forward_test(self, pred_dict, batch):
        outputs = self.get_hand_model_pose_test(pred_dict)
        assignments = self.matcher(outputs, batch)
        matched_preds, matched_targets = self.get_matched_by_assignment(outputs, batch, assignments)
        
        outputs['matched'] = matched_preds
        batch['matched'] = matched_targets
        outputs['matched']['scene_pc'] = batch['scene_pc']
        outputs['matched']['obj_verts'] = batch['obj_verts']
        outputs['matched']['obj_faces'] = batch['obj_faces']
        
        return self._calculate_metrics(outputs, batch)

    def forward_metric(self, pred_dict, batch):
        """
        Forward method for metric calculation (compatibility with diffuser_lightning.py)
        This is an alias for the test mode forward method.
        """
        return self._forward_test(pred_dict, batch)

    def _calculate_losses(self, outputs, targets):
        losses = {}

        for name, _ in self.loss_weights.items():
            if name == 'neg_loss':
                # Handle negative prompt loss
                if 'neg_pred' in outputs['matched'] and 'neg_text_features' in outputs['matched']:
                    neg_loss = self._calculate_negative_prompt_loss(
                        outputs['matched']['neg_pred'],
                        outputs['matched']['neg_text_features']
                    )
                    losses['neg_loss'] = neg_loss
                else:
                    # If no negative prompt data, set to 0
                    device = self._get_device(*outputs.values()) if outputs else torch.device('cpu')
                    losses['neg_loss'] = torch.tensor(0.0, device=device)
            elif hasattr(self, f"get_{name}_loss"):
                loss_method = getattr(self, f"get_{name}_loss")
                _loss_dict = loss_method(outputs, targets)
                losses.update(_loss_dict)
            else:
                available_loss = [x[4:] for x in dir(self) if x.endswith("_loss") and not x.startswith("_")]
                raise NotImplementedError(f"Unable to calculate {name} loss. Available losses: {available_loss}")

        # Add multi-grasp specific losses
        if self.use_consistency_loss:
            consistency_loss_dict = self.get_consistency_loss(outputs, targets)
            losses.update(consistency_loss_dict)

        # Add diversity loss if enabled
        if hasattr(self, 'diversity_loss_weight') and self.diversity_loss_weight > 0:
            diversity_loss_dict = self.get_diversity_loss(outputs, targets)
            losses.update(diversity_loss_dict)

        return losses

    def _calculate_negative_prompt_loss(self, neg_pred, neg_text_features):
        """
        Calculate negative prompt loss
        
        Design concept:
        - negative_net predicts distractor embeddings from "scene-target" difference
        - Loss function makes predicted distractor embedding close to any real distractor
        - Uses min operation: only need to successfully predict any one distractor
        """
        if neg_pred is None or neg_text_features is None:
            device = self._get_device(neg_pred, neg_text_features)
            return torch.tensor(0.0, device=device)

        try:
            # Expand prediction embedding to match negative prompt dimensions for broadcast computation
            B, num_neg_prompts, embed_dim = neg_text_features.shape
            neg_pred_expanded = neg_pred.unsqueeze(1).expand(B, num_neg_prompts, embed_dim)

            # Calculate Euclidean distance between predicted distractor and each real distractor
            paired_distances = torch.sqrt(
                torch.sum((neg_pred_expanded - neg_text_features)**2, dim=2) + 1e-8
            )  # (B, num_neg_prompts)

            # Find minimum distance (closest distractor) for each sample
            min_distances = torch.min(paired_distances, dim=1)[0]  # (B,)

            # Average over entire batch
            neg_loss = torch.mean(min_distances)

            return neg_loss

        except Exception as e:
            logging.warning(f"Negative prompt loss calculation failed: {e}")
            device = self._get_device(neg_pred, neg_text_features)
            return torch.tensor(0.0, device=device)

    def _calculate_metrics(self, pred_dict, batch):
        """
        Calculate evaluation metrics supporting both single and multi-grasp formats.

        For multi-grasp format, computes metrics for all grasps and provides both
        per-grasp and aggregated statistics.
        """
        hand_model_pose = batch['matched']['hand_model_pose']
        obj_verts = pred_dict['matched']['obj_verts']
        obj_faces = pred_dict['matched']['obj_faces']

        # Detect input format
        if hand_model_pose.dim() == 2:
            # Single grasp format: [B, pose_dim]
            return self._calculate_metrics_single_grasp(pred_dict, batch)
        elif hand_model_pose.dim() == 3:
            # Multi grasp format: [B, num_grasps, pose_dim]
            return self._calculate_metrics_multi_grasp(pred_dict, batch)
        else:
            raise ValueError(f"Unsupported hand_model_pose dimension: {hand_model_pose.dim()}")

    def _calculate_metrics_single_grasp(self, pred_dict, batch):
        """Calculate metrics for single grasp format (backward compatibility)"""
        q1_list = []
        pen_list = []
        valid_q1_list = []
        metric_details = {}

        hand_model_pose = batch['matched']['hand_model_pose']
        obj_verts = pred_dict['matched']['obj_verts']
        obj_faces = pred_dict['matched']['obj_faces']

        for i in range(hand_model_pose.shape[0]):
            q1 = cal_q1(self.q1_cfg, self.hand_model,
                       obj_verts[i],
                       obj_faces[i],
                       self.scale,
                       hand_model_pose[i].unsqueeze(0))

            pen = cal_pen(self.q1_cfg, self.hand_model,
                         obj_verts[i],
                         obj_faces[i],
                         self.scale,
                         hand_model_pose[i].unsqueeze(0))

            # Convert tensor results to float if needed
            q1_val = q1.item() if torch.is_tensor(q1) else q1
            pen_val = pen.item() if torch.is_tensor(pen) else pen

            q1_list.append(q1_val)
            pen_list.append(pen_val)
            # thres_pen = self.q1_cfg.get('thres_pen') if hasattr(self.q1_cfg, 'get') else self.q1_cfg['thres_pen']
            thres_pen = self.q1_cfg['thres_pen']
            valid = (pen_val < thres_pen)
            valid_q1 = q1_val if valid else 0
            valid_q1_list.append(valid_q1)

            metric_details[f"{batch['obj_code'][i]}_{batch['scene_id'][i]}_{batch['category_id_from_object_index'][i]}_{batch['depth_view_index'][i]}"] = {
                "q1": q1_val,
                "valid_q1": valid_q1,
                "pen": pen_val
            }

        metric_dict = {
            "mean_q1": float(mean(q1_list)),
            "mean_pen": float(mean(pen_list)),
            "max_pen": float(max(pen_list)),
            "mean_valid_q1": float(mean(valid_q1_list)),
        }
        return metric_dict, metric_details

    def _calculate_metrics_multi_grasp(self, pred_dict, batch):
        """Calculate metrics for multi-grasp format with batch processing"""
        hand_model_pose = batch['matched']['hand_model_pose']  # [B, num_grasps, pose_dim]
        obj_verts = pred_dict['matched']['obj_verts']  # [B, V, 3]
        obj_faces = pred_dict['matched']['obj_faces']  # [B, F, 3]

        B, num_grasps, pose_dim = hand_model_pose.shape

        # Batch process all samples
        q1_results = []
        pen_results = []
        metric_details = {}

        for i in range(B):
            # Calculate Q1 and penetration for all grasps of current sample
            q1_batch = cal_q1(self.q1_cfg, self.hand_model,
                             obj_verts[i],
                             obj_faces[i],
                             self.scale,
                             hand_model_pose[i])  # [num_grasps]

            pen_batch = cal_pen(self.q1_cfg, self.hand_model,
                               obj_verts[i],
                               obj_faces[i],
                               self.scale,
                               hand_model_pose[i])  # [num_grasps]

            # Convert to numpy for easier processing
            q1_vals = q1_batch.cpu().numpy() if torch.is_tensor(q1_batch) else np.array([q1_batch])
            pen_vals = pen_batch.cpu().numpy() if torch.is_tensor(pen_batch) else np.array([pen_batch])

            q1_results.extend(q1_vals.tolist())
            pen_results.extend(pen_vals.tolist())

            # Store per-grasp details
            for j in range(num_grasps):
                q1_val = q1_vals[j] if len(q1_vals) > j else q1_vals[0]
                pen_val = pen_vals[j] if len(pen_vals) > j else pen_vals[0]
                # thres_pen = self.q1_cfg.get('thres_pen') if hasattr(self.q1_cfg, 'get') else self.q1_cfg['thres_pen']
                thres_pen = self.q1_cfg['thres_pen']
                valid = (pen_val < thres_pen)
                valid_q1 = q1_val if valid else 0

                key = f"{batch['obj_code'][i]}_{batch['scene_id'][i]}_{batch['category_id_from_object_index'][i]}_{batch['depth_view_index'][i]}_grasp{j}"
                metric_details[key] = {
                    "q1": float(q1_val),
                    "valid_q1": float(valid_q1),
                    "pen": float(pen_val)
                }

        # Calculate aggregated metrics
        valid_q1_list = [details["valid_q1"] for details in metric_details.values()]

        # Multi-grasp specific metrics
        q1_array = np.array(q1_results)
        pen_array = np.array(pen_results)

        # Best grasp metrics (minimum penetration, maximum Q1 among valid grasps)
        best_grasp_indices = []
        best_q1_list = []
        best_pen_list = []

        for i in range(B):
            start_idx = i * num_grasps
            end_idx = (i + 1) * num_grasps
            sample_q1 = q1_array[start_idx:end_idx]
            sample_pen = pen_array[start_idx:end_idx]

            # Find best grasp (lowest penetration, then highest Q1)
            # thres_pen = self.q1_cfg.get('thres_pen') if hasattr(self.q1_cfg, 'get') else self.q1_cfg['thres_pen']
            thres_pen = self.q1_cfg['thres_pen']
            valid_mask = sample_pen < thres_pen
            if valid_mask.any():
                valid_indices = np.where(valid_mask)[0]
                best_idx = valid_indices[np.argmax(sample_q1[valid_indices])]
            else:
                best_idx = np.argmin(sample_pen)

            best_grasp_indices.append(best_idx)
            best_q1_list.append(sample_q1[best_idx])
            best_pen_list.append(sample_pen[best_idx])

        metric_dict = {
            # Overall metrics (all grasps)
            "mean_q1": float(np.mean(q1_results)),
            "mean_pen": float(np.mean(pen_results)),
            "max_pen": float(np.max(pen_results)),
            "mean_valid_q1": float(mean(valid_q1_list)),

            # Multi-grasp specific metrics
            "std_q1": float(np.std(q1_results)),
            "std_pen": float(np.std(pen_results)),
            "min_q1": float(np.min(q1_results)),
            "max_q1": float(np.max(q1_results)),
            "min_pen": float(np.min(pen_results)),

            # Best grasp metrics
            "best_mean_q1": float(np.mean(best_q1_list)),
            "best_mean_pen": float(np.mean(best_pen_list)),
            "best_max_pen": float(np.max(best_pen_list)),
            "best_mean_valid_q1": float(np.mean([q1 if pen < thres_pen else 0
                                               for q1, pen in zip(best_q1_list, best_pen_list)])),

            # Success rate metrics
            "success_rate": float(np.mean(pen_array < thres_pen)),
            "best_success_rate": float(np.mean(np.array(best_pen_list) < thres_pen)),
        }

        return metric_dict, metric_details

    def get_hand_model_pose(self, outputs, targets):
        targets['translation_norm'] = targets['norm_pose'][...,:3]
        targets['qpos_norm'] = targets['norm_pose'][...,3:19]
        targets['rotation'] = targets['norm_pose'][...,19:]
        
        outputs['translation_norm'] = outputs['pred_pose_norm'][...,:3]
        outputs['qpos_norm'] = outputs['pred_pose_norm'][...,3:19]
        outputs['rotation'] = outputs['pred_pose_norm'][...,19:]
        
        hand_model_pose = denorm_hand_pose_robust(outputs["pred_pose_norm"], self.rot_type, self.mode)
        outputs['hand_model_pose'] = hand_model_pose
        outputs = {'matched': outputs}
        targets = {'matched': targets}
        return outputs, targets

    def get_hand_model_pose_test_withT(self, outputs, targets):
        if outputs['pred_pose_norm'].dim() < 3:
            outputs['pred_pose_norm'] = outputs['pred_pose_norm'].unsqueeze(1)
        if targets['norm_pose'].dim() < 3:
            targets['norm_pose'] = targets['norm_pose'].unsqueeze(1)
            targets['hand_model_pose'] = targets['hand_model_pose'].unsqueeze(1)
        
        outputs['translation_norm'] = outputs['pred_pose_norm'][...,:3]
        outputs['qpos_norm'] = outputs['pred_pose_norm'][...,3:19]
        outputs['rotation'] = outputs['pred_pose_norm'][...,19:]

        hand_model_pose = denorm_hand_pose_robust(outputs['pred_pose_norm'], self.rot_type, self.mode)
        outputs['hand_model_pose'] = hand_model_pose
        return outputs, targets

    def get_hand_model_pose_test(self, outputs):
        if outputs['pred_pose_norm'].dim() < 3:
            outputs['pred_pose_norm'] = outputs['pred_pose_norm'].unsqueeze(1)
        
        outputs['translation_norm'] = outputs['pred_pose_norm'][...,:3]
        outputs['qpos_norm'] = outputs['pred_pose_norm'][...,3:19]
        outputs['rotation'] = outputs['pred_pose_norm'][...,19:]
       
        hand_model_pose = denorm_hand_pose_robust(outputs['pred_pose_norm'], self.rot_type, self.mode)
        outputs['hand_model_pose'] = hand_model_pose
        return outputs

    def _prepare_hand_data(self, outputs, targets, hand_model_kwargs: Dict[str, bool]):
        # Determine if scene_pc is available and needed
        scene_pc_for_targets = targets.get('scene_pc') 
        if 'matched' in targets and 'scene_pc' in targets['matched']:
            scene_pc_for_targets = targets['matched']['scene_pc']

        # outputs hand model uses scene_pc from targets
        scene_pc_for_outputs = scene_pc_for_targets
            
        targets['hand'] = self.hand_model(
            targets['matched']['hand_model_pose'], 
            scene_pc=scene_pc_for_targets,
            **hand_model_kwargs
        )
        outputs['hand'] = self.hand_model(
            outputs['matched']['hand_model_pose'], 
            scene_pc=scene_pc_for_outputs,
            **hand_model_kwargs
        )

        outputs['rot_type'] = self.rot_type
        return outputs, targets

    def get_hand_any_norm(self, norm_pose, scene_pc=None):
        """Get hand model output from normalized pose"""
        hand_model_pose = denorm_hand_pose_robust(norm_pose, self.rot_type, self.mode)
        hand = self.hand_model(
            hand_model_pose, 
            scene_pc=scene_pc,
            **self.test_infer_hand_model_kwargs
        )
        return hand

    def get_hand(self, outputs, targets):
        """
        Get hand model output from outputs and targets (compatibility with diffuser_lightning.py)
        This method extracts hand model data from the processed outputs and targets.
        """
        # Return hand model data from both outputs and targets
        return outputs['hand'], targets['hand']

    def print_matcher_results(self, matcher_output):
        """Print matcher results for debugging"""
        print("=== Matcher Results ===")
        
        print(f"Cost Matrix Shape: {matcher_output['final_cost'].shape}")
        
        for batch_idx, assign in enumerate(matcher_output['assignments']):
            queries, targets = assign
            print(f"Batch {batch_idx} Matches:")
            matched_count = len(queries)
            total_count = matcher_output['query_matched_mask'][batch_idx].numel()
            print(f"  Matched Queries: {matched_count}/{total_count}")
            for q, t in zip(queries.cpu().numpy(), targets.cpu().numpy()):
                print(f"  Query {q} -> Target {t}")
        
        print("\nMatching Statistics:")
        matched_queries = matcher_output['query_matched_mask'].sum().item()
        total_queries = matcher_output['query_matched_mask'].numel()
        print(f"Total Matched Queries: {matched_queries}/{total_queries}")

    def infer_norm_process_dict_get_pose(self, outputs, targets):
        outputs, targets = self._infer_and_match(outputs, targets, print_matcher=True)
        return outputs, targets

    def infer_norm_process_dict_txt(self, outputs, targets):
        outputs, targets, _, _ = self._infer_and_match(outputs, targets, use_target_pose=True)
        return outputs['hand'], targets['hand']

    def infer_norm_process_dict_get_pose_matched(self, outputs, targets):
        outputs, targets, matched_preds, matched_targets = self._infer_and_match(outputs, targets, print_matcher=True)
        return matched_preds, matched_targets, outputs, targets
    
    def infer_norm_process_dict(self, outputs, targets):
        outputs, targets, _, _ = self._infer_and_match(outputs, targets)
        return outputs['hand'], targets['hand']

    def _infer_and_match(self, outputs, targets, use_target_pose=False, print_matcher=False):
        """Helper function to process inference dictionaries, match, and prepare hand data."""
        if use_target_pose:
            outputs, targets = self.get_hand_model_pose_test_withT(outputs, targets)
        else:
            outputs = self.get_hand_model_pose_test(outputs)

        assignments = self.matcher(outputs, targets)
        if print_matcher:
            self.print_matcher_results(assignments)

        matched_preds, matched_targets = self.get_matched_by_assignment(outputs, targets, assignments)
        outputs['matched'] = matched_preds
        targets['matched'] = matched_targets
        targets['matched']['scene_pc'] = targets['scene_pc']

        outputs, targets = self._prepare_hand_data(outputs, targets, self.test_infer_hand_model_kwargs)
        return outputs, targets, matched_preds, matched_targets

    def get_matched_by_assignment(self, predictions: Dict, targets: Dict, assignment: Dict) -> Tuple[Dict, Dict]:
        """
        Extracts corresponding targets for each prediction based on matching assignments,
        preserving the batch structure.

        Under the new assumption that num_targets >= num_queries for each sample, every
        prediction is guaranteed to have a match. This function uses the assignment indices
        to gather the corresponding ground truth targets, producing a target tensor of
        shape [B, num_queries, D] that is aligned with the predictions.

        Args:
            predictions: Dictionary of predictions from the model. Expected shapes like [B, num_queries, D].
            targets: Dictionary of ground truth targets. Expected shapes like [B, num_targets, D].
            assignment: The output dictionary from the Matcher, containing 'per_query_gt_inds'.

        Returns:
            matched_preds: The original predictions, returned for API consistency.
                           Each value is a tensor of shape [B, num_queries, D].
            matched_targets: A dict where each value is a tensor of shape [B, num_queries, D]
                             containing the matched targets, aligned with predictions.
        """
        per_query_gt_inds = assignment["per_query_gt_inds"]  # Shape: [B, num_queries]

        matched_preds, matched_targets = {}, {}

        pred_target_match_key_map = {
            "pred_pose_norm": "norm_pose",
            "hand_model_pose": "hand_model_pose",
        }

        # Since every prediction is matched, the matched predictions are the original predictions.
        # We just need to gather the corresponding targets.
        for pred_key, target_key in pred_target_match_key_map.items():
            if pred_key not in predictions:
                continue

            pred = predictions[pred_key]
            target = targets[target_key]

            # Ensure prediction is in [B, N_query, D] format for consistency
            if pred.dim() == 2:
                pred = pred.unsqueeze(1)

            # The matched predictions are simply the original predictions, as their order is preserved.
            matched_preds[pred_key] = pred

            # Gather the targets corresponding to each prediction using the indices from the matcher.
            # `per_query_gt_inds` has shape [B, num_queries]
            # `target` has shape [B, num_targets, D]
            # We want to select `target[b, per_query_gt_inds[b], :]` for each b.
            B, N_queries, _ = pred.shape
            _B, _N_targets, D_target = target.shape

            # Expand index dimensions to match the target dimension for `gather`
            idx = per_query_gt_inds.unsqueeze(-1).expand(B, N_queries, D_target)

            # Gather targets along the num_targets dimension (dim=1)
            gathered_targets = torch.gather(target, 1, idx)
            matched_targets[target_key] = gathered_targets

        return matched_preds, matched_targets

    # ==================== Specific Loss Functions ====================
    
    def get_latent_loss(self, prediction, target) -> Dict[str, Tensor]:
        """Calculate latent space KL divergence loss for variational models"""
        log_var_tensor, means_tensor = None, None

        # Try to get log_var and means from prediction['matched'] (training path)
        if 'matched' in prediction and isinstance(prediction['matched'], dict):
            log_var_tensor = prediction['matched'].get('log_var')
            means_tensor = prediction['matched'].get('means')

        # Fallback to top-level prediction (CVAE validation path)
        if log_var_tensor is None and 'log_var' in prediction:
            log_var_tensor = prediction.get('log_var')
        if means_tensor is None and 'means' in prediction:
            means_tensor = prediction.get('means')

        # Determine device for zero tensor fallback
        tensor_for_device = None
        if 'matched' in target and isinstance(target['matched'], dict) and 'norm_pose' in target['matched']:
            tensor_for_device = target['matched']['norm_pose']
        elif 'norm_pose' in target:
            tensor_for_device = target['norm_pose']

        if log_var_tensor is None or means_tensor is None:
            expected_device = torch.device('cpu')
            if tensor_for_device is not None and hasattr(tensor_for_device, 'device'):
                expected_device = tensor_for_device.device
            
            logging.warning(f"Missing 'log_var' or 'means' for latent loss calculation. Returning 0 KLD loss on device {expected_device}.")
            return {"latent": torch.tensor(0.0, device=expected_device)}

        # Calculate KL divergence loss
        kld = -0.5 * torch.sum(1 + log_var_tensor - means_tensor.pow(2) - log_var_tensor.exp()) / log_var_tensor.shape[0]
        return {"latent": kld}
    
    def get_para_loss(self, prediction, target) -> Dict[str, Tensor]:
        """Calculate parameter reconstruction loss with multi-grasp support"""
        pred_para = prediction['matched']['pred_pose_norm']
        para = target['matched']['norm_pose']

        # Handle multi-grasp format
        if para.dim() == 3:
            # Multi-grasp format: [B, num_grasps, pose_dim]
            B, num_grasps, pose_dim = para.shape
            mse_loss = F.mse_loss(pred_para, para, reduction='none')  # [B, num_grasps, pose_dim]

            # Apply aggregation strategy
            if self.loss_aggregation == 'mean':
                para_loss = mse_loss.mean()
            elif self.loss_aggregation == 'sum':
                para_loss = mse_loss.sum() / B  # Normalize by batch size
            elif self.loss_aggregation == 'weighted':
                # Use grasp weights if available
                weights = target.get('grasp_weights', torch.ones(B, num_grasps, device=para.device))
                weighted_loss = mse_loss.mean(dim=-1) * weights  # [B, num_grasps]
                para_loss = weighted_loss.sum() / weights.sum()
            else:
                para_loss = mse_loss.mean()
        else:
            # Single grasp format (backward compatibility)
            para_loss = F.mse_loss(pred_para, para)

        return {"para": para_loss}

    def get_noise_loss(self, prediction, target) -> Dict[str, Tensor]:
        """Calculate noise prediction loss for denoising models with multi-grasp support"""
        pred_noise = prediction['matched']['pred_noise']
        noise = prediction['matched']['noise']

        # Handle multi-grasp format
        if noise.dim() == 3:
            # Multi-grasp format: [B, num_grasps, pose_dim]
            B, num_grasps, pose_dim = noise.shape
            mse_loss = F.mse_loss(pred_noise, noise, reduction='none')  # [B, num_grasps, pose_dim]

            # Apply aggregation strategy
            if self.loss_aggregation == 'mean':
                noise_loss = mse_loss.mean()
            elif self.loss_aggregation == 'sum':
                noise_loss = mse_loss.sum() / B  # Normalize by batch size
            elif self.loss_aggregation == 'weighted':
                # Use grasp weights if available
                weights = target.get('grasp_weights', torch.ones(B, num_grasps, device=noise.device))
                weighted_loss = mse_loss.mean(dim=-1) * weights  # [B, num_grasps]
                noise_loss = weighted_loss.sum() / weights.sum()
            else:
                noise_loss = mse_loss.mean()
        else:
            # Single grasp format (backward compatibility)
            noise_loss = F.mse_loss(pred_noise, noise)

        return {"noise": noise_loss}
    
    def get_translation_loss(self, prediction, target) -> Dict[str, Tensor]:
        """Calculate translation parameter reconstruction loss with multi-grasp support"""
        pred = prediction['matched']['pred_pose_norm'][...,:3]
        target_trans = target['matched']['norm_pose'][...,:3]

        # Handle multi-grasp format
        if target_trans.dim() == 3:
            # Multi-grasp format: [B, num_grasps, 3]
            B, num_grasps, _ = target_trans.shape
            mse_loss = F.mse_loss(pred, target_trans, reduction='none')  # [B, num_grasps, 3]

            # Apply aggregation strategy
            if self.loss_aggregation == 'mean':
                translation_loss = mse_loss.mean()
            elif self.loss_aggregation == 'sum':
                translation_loss = mse_loss.sum() / B
            elif self.loss_aggregation == 'weighted':
                weights = target.get('grasp_weights', torch.ones(B, num_grasps, device=target_trans.device))
                weighted_loss = mse_loss.mean(dim=-1) * weights  # [B, num_grasps]
                translation_loss = weighted_loss.sum() / weights.sum()
            else:
                translation_loss = mse_loss.mean()
        else:
            # Single grasp format (backward compatibility)
            translation_loss = self._get_regression_loss(pred, target_trans)

        loss = {"translation": translation_loss}
        return loss

    def get_qpos_loss(self, prediction, target) -> Dict[str, Tensor]:
        """Calculate joint position reconstruction loss with multi-grasp support"""
        pred = prediction['matched']['pred_pose_norm'][...,3:19]
        target_qpos = target['matched']['norm_pose'][...,3:19]

        # Handle multi-grasp format
        if target_qpos.dim() == 3:
            # Multi-grasp format: [B, num_grasps, 16]
            B, num_grasps, _ = target_qpos.shape
            mse_loss = F.mse_loss(pred, target_qpos, reduction='none')  # [B, num_grasps, 16]

            # Apply aggregation strategy
            if self.loss_aggregation == 'mean':
                qpos_loss = mse_loss.mean()
            elif self.loss_aggregation == 'sum':
                qpos_loss = mse_loss.sum() / B
            elif self.loss_aggregation == 'weighted':
                weights = target.get('grasp_weights', torch.ones(B, num_grasps, device=target_qpos.device))
                weighted_loss = mse_loss.mean(dim=-1) * weights  # [B, num_grasps]
                qpos_loss = weighted_loss.sum() / weights.sum()
            else:
                qpos_loss = mse_loss.mean()
        else:
            # Single grasp format (backward compatibility)
            qpos_loss = self._get_regression_loss(pred, target_qpos)

        loss = {"qpos": qpos_loss}
        return loss

    def get_rotation_loss(self, prediction, target) -> Dict[str, Tensor]:
        """Calculate rotation loss based on rotation type with multi-grasp support"""
        rot_type = prediction['rot_type']
        pred = prediction['matched']['pred_pose_norm'][...,19:]
        target_rot = target['matched']['norm_pose'][...,19:]

        if hasattr(self, f"_get_{rot_type}_loss"):
            loss_method = getattr(self, f"_get_{rot_type}_loss")

            # Handle multi-grasp format
            if target_rot.dim() == 3:
                # Multi-grasp format: [B, num_grasps, rot_dim]
                B, num_grasps, rot_dim = target_rot.shape

                # Flatten for rotation loss calculation
                pred_flat = pred.reshape(B * num_grasps, rot_dim)
                target_flat = target_rot.reshape(B * num_grasps, rot_dim)

                # Calculate rotation loss
                loss_dict = loss_method(pred_flat, target_flat)
                rotation_loss = loss_dict['rotation']

                # Apply aggregation strategy
                if self.loss_aggregation == 'mean':
                    # Loss is already averaged over flattened dimension
                    pass
                elif self.loss_aggregation == 'sum':
                    rotation_loss = rotation_loss * num_grasps  # Scale back up
                elif self.loss_aggregation == 'weighted':
                    # For rotation loss, we'll use mean aggregation as weights are complex to apply
                    pass

                return {"rotation": rotation_loss}
            else:
                # Single grasp format (backward compatibility)
                loss = loss_method(pred, target_rot)
                return loss
        else:
            raise NotImplementedError(f"Unable to calculate {rot_type} loss.")

    def get_hand_chamfer_loss(self, prediction, target) -> Dict[str, Tensor]:
        """Calculate Chamfer distance loss between predicted and target hand point clouds"""
        pred_hand_pc = prediction['hand']['surface_points']
        target_hand_pc = target['hand']['surface_points']
        chamfer_loss = chamfer_distance(
            pred_hand_pc, 
            target_hand_pc, 
            point_reduction="sum", 
            batch_reduction="mean"
        )[0]
        return {"hand_chamfer": chamfer_loss}

    def get_obj_penetration_loss(self, prediction, target) -> Dict[str, Tensor]:
        """Calculate object penetration loss"""
        batch_size = prediction['hand']['penetration_keypoints'].size(0)
        # Signed squared distances from object_pc to hand, inside positive, outside negative
        distances = prediction['hand']['penetration']
        # Penetration loss - only penalize positive distances (inside object)
        loss_pen = distances[distances > 0].sum() / batch_size
        return {"obj_penetration": loss_pen}

    def get_self_penetration_loss(self, prediction, target) -> Dict[str, Tensor]:
        """Calculate self-penetration loss to prevent hand self-intersection"""
        batch_size = prediction['hand']['penetration_keypoints'].size(0)
        penetration_keypoints = prediction['hand']['penetration_keypoints']
        
        # Calculate pairwise distances between penetration keypoints
        dis_spen = (penetration_keypoints.unsqueeze(1) - penetration_keypoints.unsqueeze(2) + 1e-13).square().sum(3).sqrt()
        # Avoid self-distance (set very large value for same points)
        dis_spen = torch.where(dis_spen < 1e-6, 1e6 * torch.ones_like(dis_spen), dis_spen)
        # Penalize distances smaller than threshold (0.02)
        dis_spen = 0.02 - dis_spen
        dis_spen[dis_spen < 0] = 0
        loss_spen = dis_spen.sum() / batch_size
        return {"self_penetration": loss_spen}
    
    def get_distance_loss(self, prediction, target, thres_dis=0.01) -> Dict[str, Tensor]:
        """Calculate contact distance loss"""
        dis_pred = prediction['hand']['contact_candidates_dis']
        small_dis_pred = dis_pred < thres_dis ** 2
        loss_dis = dis_pred[small_dis_pred].sum() / dis_pred.size(0)
        return {"distance": loss_dis}
    
    def get_cmap_loss(self, prediction, target, normalize_factor=200) -> Dict[str, Tensor]:
        """Calculate contact map loss"""
        # Calculate contact maps using sigmoid normalization
        cmap = 2 - 2 * torch.sigmoid(normalize_factor * (target["hand"]['distances'].abs() + 1e-4).sqrt())
        cmap_pred = 2 - 2 * torch.sigmoid(normalize_factor * (prediction["hand"]['distances'].abs() + 1e-4).sqrt())
        loss_cmap = torch.nn.functional.mse_loss(cmap, cmap_pred, reduction='sum') / cmap.size(0)
        return {"cmap": loss_cmap}
    
    # ==================== Rotation-Specific Loss Functions ====================

    def _get_euler_loss(self, prediction, target) -> Dict[str, Tensor]:
        """Calculate Euler angle rotation loss"""
        error = (prediction - target).abs().sum(-1).mean()
        return {"rotation": error}

    def _get_quat_loss(self, prediction_raw: Tensor, target_unit: Tensor) -> Dict[str, Tensor]:
        """
        Calculate quaternion rotation loss measuring angular difference
        
        Args:
            prediction_raw: Raw quaternion predictions of size (K, 4) - NOT unit quaternions
            target_unit: Target unit quaternions of size (K, 4) - ARE unit quaternions
        """
        # Normalize raw predictions to unit quaternions
        epsilon = 1e-8  # Numerical stability
        prediction_norm = torch.linalg.norm(prediction_raw, dim=-1, keepdim=True)
        normalized_prediction = prediction_raw / (prediction_norm + epsilon)

        # Calculate dot product between normalized prediction and unit target
        # For unit quaternions p and q, p·q = cos(theta/2) where theta is rotation angle
        dot_product = (normalized_prediction * target_unit).sum(dim=-1)

        # Calculate loss: 1 - |dot_product|
        # abs() handles q and -q representing same rotation
        # Loss range [0, 1]: 0 = perfect alignment, 1 = 90° difference in 4D space
        rotation_loss = 1.0 - dot_product.abs()
        mean_rotation_loss = rotation_loss.mean()

        return {"rotation": mean_rotation_loss}

    def _get_axis_loss(self, prediction, target) -> Dict[str, Tensor]:
        """Calculate axis-angle rotation loss"""
        return {"rotation": self._get_regression_loss(prediction, target)}

    def _get_r6d_loss(self, prediction, target) -> Dict[str, Tensor]:
        """Calculate 6D rotation representation loss"""
        return {"rotation": self._get_regression_loss(prediction, target)}

    def _get_regression_loss(self, prediction: Tensor, target: Tensor) -> Tensor:
        """Calculate regression loss using Huber loss"""
        loss = huber_loss(prediction - target).sum(-1).mean()
        return loss

    # ==================== Multi-Grasp Loss Functions ====================

    def get_consistency_loss(self, prediction, target) -> Dict[str, Tensor]:
        """
        Calculate grasp consistency loss to encourage similar grasps for similar scene regions

        Args:
            prediction: Prediction dictionary
            target: Target dictionary

        Returns:
            Dictionary containing consistency loss
        """
        # logging.info(f'[Consistency Loss] use_consistency_loss: {self.use_consistency_loss}, prediction keys: {list(prediction.keys())}')
        
        if not self.use_consistency_loss:
            logging.info('[Consistency Loss] Skipping consistency loss (disabled in config)')
            # Determine device from available tensors
            device_tensor = prediction.get('matched', {}).get('pred_pose_norm')
            if device_tensor is None:
                device_tensor = prediction.get('pred_pose_norm')
            device = device_tensor.device if device_tensor is not None else next(self.parameters()).device
            return {"consistency": torch.tensor(0.0, device=device)}

        if 'matched' not in prediction or 'pred_pose_norm' not in prediction['matched']:
            logging.warning(f'[Consistency Loss] Missing pred_pose_norm in prediction[\'matched\']. Available keys: {list(prediction.keys())}')
            return {"consistency": torch.tensor(0.0, device=next(self.parameters()).device)}

        pred_poses = prediction['matched']['pred_pose_norm']
            
        # logging.info(f'[Consistency Loss] Input pred_poses shape: {pred_poses.shape}, dim: {pred_poses.dim()}')

        # Only apply to multi-grasp format
        if pred_poses.dim() != 3:
            logging.warning(f'[Consistency Loss] Expected 3D tensor, got {pred_poses.dim()}D tensor')
            return {"consistency": torch.tensor(0.0, device=pred_poses.device)}

        B, num_grasps, pose_dim = pred_poses.shape

        # Calculate pairwise similarities between grasps
        consistency_loss = 0.0
        for b in range(B):
            pred_b = pred_poses[b]  # [num_grasps, pose_dim]

            # Calculate pairwise distances between grasps
            distances = torch.cdist(pred_b, pred_b, p=2)  # [num_grasps, num_grasps]

            # Exclude diagonal elements (self-distance)
            mask = ~torch.eye(num_grasps, dtype=torch.bool, device=distances.device)
            distances_masked = distances[mask]

            # Consistency loss: encourage moderate diversity (not too similar, not too different)
            # Target distance around 0.5 (adjust based on pose normalization)
            target_distance = 0.5
            consistency_loss += F.mse_loss(distances_masked,
                                         torch.full_like(distances_masked, target_distance))

        consistency_loss = consistency_loss / B
        return {"consistency": consistency_loss}

    def get_diversity_loss(self, prediction, target) -> Dict[str, Tensor]:
        """
        Calculate diversity loss to encourage diverse grasp predictions

        Args:
            prediction: Prediction dictionary
            target: Target dictionary

        Returns:
            Dictionary containing diversity loss
        """
        # logging.info(f'[Diversity Loss] prediction keys: {list(prediction.keys())}')
        
        if 'matched' not in prediction or 'pred_pose_norm' not in prediction['matched']:
            logging.warning(f'[Diversity Loss] Missing pred_pose_norm in prediction[\'matched\']. Available keys: {list(prediction.keys())}')
            return {"diversity": torch.tensor(0.0, device=next(self.parameters()).device)}

        pred_poses = prediction['matched']['pred_pose_norm']

        # logging.info(f'[Diversity Loss] Input pred_poses shape: {pred_poses.shape}, dim: {pred_poses.dim()}')

        # Only apply to multi-grasp format
        if pred_poses.dim() != 3:
            logging.warning(f'[Diversity Loss] Expected 3D tensor, got {pred_poses.dim()}D tensor')
            return {"diversity": torch.tensor(0.0, device=pred_poses.device)}

        B, num_grasps, pose_dim = pred_poses.shape

        diversity_scores = []
        for b in range(B):
            pred_b = pred_poses[b]  # [num_grasps, pose_dim]

            # Calculate pairwise distances
            distances = torch.cdist(pred_b, pred_b, p=2)  # [num_grasps, num_grasps]

            # Exclude diagonal elements
            mask = ~torch.eye(num_grasps, dtype=torch.bool, device=distances.device)
            distances_masked = distances[mask]

            # Diversity score: negative of mean distance (we want to maximize diversity)
            diversity_score = -distances_masked.mean()
            diversity_scores.append(diversity_score)

        diversity_loss = torch.stack(diversity_scores).mean()
        return {"diversity": diversity_loss}


def huber_loss(error, delta=1.0):
    """
    Huber loss function for robust regression

    Reference: https://github.com/charlesq34/frustum-pointnets/blob/master/models/model_util.py

    Formula:
    - 0.5 * |x|^2                 if |x| <= delta
    - 0.5 * delta^2 + delta * (|x| - delta)     if |x| > delta

    Args:
        error: Error tensor (pred - gt or dist(pred, gt))
        delta: Huber loss threshold

    Returns:
        Huber loss tensor
    """
    abs_error = torch.abs(error)
    quadratic = torch.clamp(abs_error, max=delta)
    linear = abs_error - quadratic
    loss = 0.5 * quadratic ** 2 + delta * linear
    return loss
