"""
DDPM 验证逻辑模块
包含验证和测试步骤
"""

import torch
import logging
from typing import Dict, List, Any
from statistics import mean
from utils.hand_helper import process_hand_pose_test, denorm_hand_pose_robust
from ..utils.log_colors import HEADER, BLUE, GREEN, YELLOW, RED, ENDC, BOLD, UNDERLINE


class DDPMValidationMixin:
    """
    DDPM 验证逻辑混入类
    包含验证和测试相关的方法实现
    """
    
    def on_validation_epoch_start(self):
        """验证轮次开始时的回调"""
        self.validation_step_outputs = []
        logging.info(f'{YELLOW}=== Validation Epoch {self.current_epoch} Started ==={ENDC}')
    
    def validation_step(self, batch, batch_idx):
        """
        验证步骤：支持多抓取并行推理
        """
        # 数据预处理保持不变（已支持多抓取）
        batch = process_hand_pose_test(batch, rot_type=self.rot_type, mode=self.mode)
        
        # 使用扩散模型进行采样
        pred_x0 = self.diffusion_model.sample(batch)
        pred_x0 = pred_x0[:, 0, -1]  # 取第一次采样的最后一个时间步
        
        # 构建预测字典
        pred_dict = self._build_pred_dict(pred_x0)
        
        # 计算验证损失和指标
        loss_dict = self.criterion(pred_dict, batch, mode='val')
        loss = loss_dict.get('total_loss', sum(loss_dict.values()))
        
        # 存储结果
        self.validation_step_outputs.append({
            "loss": loss,
            "loss_dict": {k: v.item() for k, v in loss_dict.items()}
        })
        
        # 记录验证指标
        self._log_validation_metrics(loss, loss_dict, batch_idx)
        
        return loss
    
    def on_validation_epoch_end(self):
        """验证轮次结束时的回调"""
        if not self.validation_step_outputs:
            return
        
        # 计算平均指标
        avg_metrics = self._compute_average_metrics(self.validation_step_outputs)
        
        # 记录平均指标
        self._log_epoch_metrics(avg_metrics, 'val')
        
        # 打印验证结果
        self._print_validation_summary(avg_metrics)
        
        # 清空输出
        self.validation_step_outputs.clear()
    
    def test_step(self, batch, batch_idx):
        """
        测试步骤：与验证步骤类似，但用于最终评估
        """
        # 数据预处理
        batch = process_hand_pose_test(batch, rot_type=self.rot_type, mode=self.mode)
        
        # 使用扩散模型进行采样
        pred_x0 = self.diffusion_model.sample(batch)
        pred_x0 = pred_x0[:, 0, -1]  # 取第一次采样的最后一个时间步
        
        # 构建预测字典
        pred_dict = self._build_pred_dict(pred_x0)
        
        # 计算测试损失和指标
        loss_dict = self.criterion(pred_dict, batch, mode='test')
        loss = loss_dict.get('total_loss', sum(loss_dict.values()))
        
        # 记录测试指标
        self._log_test_metrics(loss, loss_dict, batch_idx)
        
        return {
            "test_loss": loss,
            "test_loss_dict": {k: v.item() for k, v in loss_dict.items()}
        }
    
    def _build_pred_dict(self, pred_x0: torch.Tensor) -> Dict[str, torch.Tensor]:
        """
        构建预测字典
        
        Args:
            pred_x0: 预测的干净数据
            
        Returns:
            pred_dict: 预测字典
        """
        if pred_x0.dim() == 3:
            # 多抓取格式 [B, num_grasps, pose_dim]
            pred_dict = {
                "pred_pose_norm": pred_x0,
                "qpos_norm": pred_x0[..., 3:19],
                "translation_norm": pred_x0[..., :3],
                "rotation": pred_x0[..., 19:],
                "pred_noise": torch.tensor([1.0], device=self.device),
                "noise": torch.tensor([1.0], device=self.device)
            }
        else:
            # 单抓取格式 [B, pose_dim]
            pred_dict = {
                "pred_pose_norm": pred_x0,
                "qpos_norm": pred_x0[..., 3:19],
                "translation_norm": pred_x0[..., :3],
                "rotation": pred_x0[..., 19:],
                "pred_noise": torch.tensor([1.0], device=self.device),
                "noise": torch.tensor([1.0], device=self.device)
            }
        
        return pred_dict
    
    def _log_validation_metrics(self, loss: torch.Tensor, loss_dict: Dict[str, torch.Tensor], batch_idx: int):
        """
        记录验证指标
        
        Args:
            loss: 总损失
            loss_dict: 损失字典
            batch_idx: 批次索引
        """
        # 主要损失日志
        self.log('val_loss', loss, on_step=False, on_epoch=True, prog_bar=True, 
                logger=True, batch_size=self.batch_size, sync_dist=True)
        
        # 详细损失日志
        for k, v in loss_dict.items():
            self.log(f'val_{k}', v, on_step=False, on_epoch=True, 
                    logger=True, batch_size=self.batch_size, sync_dist=True)
    
    def _log_test_metrics(self, loss: torch.Tensor, loss_dict: Dict[str, torch.Tensor], batch_idx: int):
        """
        记录测试指标
        
        Args:
            loss: 总损失
            loss_dict: 损失字典
            batch_idx: 批次索引
        """
        # 主要损失日志
        self.log('test_loss', loss, on_step=False, on_epoch=True, prog_bar=True, 
                logger=True, batch_size=self.batch_size, sync_dist=True)
        
        # 详细损失日志
        for k, v in loss_dict.items():
            self.log(f'test_{k}', v, on_step=False, on_epoch=True, 
                    logger=True, batch_size=self.batch_size, sync_dist=True)
    
    def _compute_average_metrics(self, outputs: List[Dict]) -> Dict[str, float]:
        """
        计算平均指标
        
        Args:
            outputs: 输出列表
            
        Returns:
            avg_metrics: 平均指标字典
        """
        if not outputs:
            return {}
        
        # 收集所有指标
        all_metrics = {}
        for output in outputs:
            loss_dict = output.get('loss_dict', {})
            for k, v in loss_dict.items():
                if k not in all_metrics:
                    all_metrics[k] = []
                all_metrics[k].append(v)
        
        # 计算平均值
        avg_metrics = {}
        for k, values in all_metrics.items():
            avg_metrics[k] = mean(values)
        
        return avg_metrics
    
    def _log_epoch_metrics(self, metrics: Dict[str, float], prefix: str):
        """
        记录轮次级别的指标
        
        Args:
            metrics: 指标字典
            prefix: 前缀（val/test）
        """
        for k, v in metrics.items():
            self.log(f'{prefix}_epoch_{k}', v, on_epoch=True, logger=True, sync_dist=True)
    
    def _print_validation_summary(self, metrics: Dict[str, float]):
        """
        打印验证摘要
        
        Args:
            metrics: 指标字典
        """
        logging.info(f'{YELLOW}=== Validation Epoch {self.current_epoch} Summary ==={ENDC}')
        for k, v in metrics.items():
            logging.info(f'{YELLOW}{k.title() + ":":<21s} {v:.4f}{ENDC}')
        logging.info(f'{YELLOW}=== Validation Epoch {self.current_epoch} Ended ==={ENDC}')
