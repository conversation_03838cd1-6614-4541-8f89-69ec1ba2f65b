"""
DDPM 训练逻辑模块
包含训练步骤和相关工具方法
"""

import torch
import logging
from typing import Dict, Any
from statistics import mean
from utils.hand_helper import process_hand_pose
from ..utils.log_colors import HEADER, BLUE, GREEN, YELLOW, RED, ENDC, BOLD, UNDERLINE


class DDPMTrainingMixin:
    """
    DDPM 训练逻辑混入类
    包含训练相关的方法实现
    """
    
    def training_step(self, batch, batch_idx):
        """
        训练步骤：支持单抓取和多抓取并行训练
        """
        # 手部姿态处理（已支持多抓取）
        batch = process_hand_pose(batch, rot_type=self.rot_type, mode=self.mode)
        
        # 使用扩散模型计算训练损失
        loss_dict = self.diffusion_model.compute_training_loss(batch, self.criterion)
        
        # 计算总损失
        loss = self._compute_total_loss(loss_dict)
        
        # 日志记录
        self._log_training_metrics(loss, loss_dict, batch_idx)
        
        return loss
    
    def _compute_total_loss(self, loss_dict: Dict[str, torch.Tensor]) -> torch.Tensor:
        """
        计算总损失
        
        Args:
            loss_dict: 损失字典
            
        Returns:
            total_loss: 总损失
        """
        loss = 0
        for k, v in loss_dict.items():
            if k in self.loss_weights:
                loss += v * self.loss_weights[k]
        
        return loss
    
    def _log_training_metrics(self, loss: torch.Tensor, loss_dict: Dict[str, torch.Tensor], batch_idx: int):
        """
        记录训练指标
        
        Args:
            loss: 总损失
            loss_dict: 损失字典
            batch_idx: 批次索引
        """
        # 主要损失日志
        self.log('train_loss', loss, on_step=True, on_epoch=True, prog_bar=True, 
                logger=True, batch_size=self.batch_size, sync_dist=True)
        
        # 详细损失日志
        for k, v in loss_dict.items():
            self.log(f'train_{k}', v, on_step=False, on_epoch=True, 
                    logger=True, batch_size=self.batch_size, sync_dist=True)
        
        # 打印训练信息
        if batch_idx % self.print_freq == 0:
            self._print_training_info(loss, loss_dict, batch_idx)
    
    def _print_training_info(self, loss: torch.Tensor, loss_dict: Dict[str, torch.Tensor], batch_idx: int):
        """
        打印训练信息
        
        Args:
            loss: 总损失
            loss_dict: 损失字典
            batch_idx: 批次索引
        """
        logging.info(f'{HEADER}=== Training Step {batch_idx} ==={ENDC}')
        logging.info(f'{BLUE}{"Total Loss:":<21s} {loss.item():.4f}{ENDC}')
        for k, v in loss_dict.items():
            logging.info(f'{BLUE}{k.title() + ":":<21s} {v.item():.4f}{ENDC}')
    
    def _sample_timesteps(self, B: int, device: torch.device, rand_t_type: str = 'uniform') -> torch.Tensor:
        """
        采样时间步
        
        Args:
            B: 批次大小
            device: 设备
            rand_t_type: 随机时间步类型
            
        Returns:
            t: 时间步
        """
        if rand_t_type == 'uniform':
            t = torch.randint(0, self.diffusion_model.timesteps, (B,), device=device).long()
        elif rand_t_type == 'antithetic':
            # 对偶采样，减少方差
            if B % 2 == 0:
                t1 = torch.randint(0, self.diffusion_model.timesteps, (B // 2,), device=device).long()
                t2 = self.diffusion_model.timesteps - 1 - t1
                t = torch.cat([t1, t2], dim=0)
            else:
                t = torch.randint(0, self.diffusion_model.timesteps, (B,), device=device).long()
        else:
            t = torch.randint(0, self.diffusion_model.timesteps, (B,), device=device).long()
        
        return t
    
    def configure_optimizers(self):
        """
        配置优化器和学习率调度器
        """
        # 构建优化器
        optimizer_cfg = self.optimizer_cfg.copy()
        optimizer_type = optimizer_cfg.pop('type')
        
        if optimizer_type == 'Adam':
            optimizer = torch.optim.Adam(self.parameters(), **optimizer_cfg)
        elif optimizer_type == 'AdamW':
            optimizer = torch.optim.AdamW(self.parameters(), **optimizer_cfg)
        elif optimizer_type == 'SGD':
            optimizer = torch.optim.SGD(self.parameters(), **optimizer_cfg)
        else:
            raise ValueError(f"Unsupported optimizer type: {optimizer_type}")
        
        # 构建学习率调度器
        if self.scheduler is None:
            return optimizer
        
        scheduler_cfg = self.scheduler.copy()
        scheduler_type = scheduler_cfg.pop('type')
        
        if scheduler_type == 'StepLR':
            scheduler = torch.optim.lr_scheduler.StepLR(optimizer, **scheduler_cfg)
        elif scheduler_type == 'MultiStepLR':
            scheduler = torch.optim.lr_scheduler.MultiStepLR(optimizer, **scheduler_cfg)
        elif scheduler_type == 'ExponentialLR':
            scheduler = torch.optim.lr_scheduler.ExponentialLR(optimizer, **scheduler_cfg)
        elif scheduler_type == 'CosineAnnealingLR':
            scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(optimizer, **scheduler_cfg)
        elif scheduler_type == 'ReduceLROnPlateau':
            scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(optimizer, **scheduler_cfg)
            return {
                'optimizer': optimizer,
                'lr_scheduler': {
                    'scheduler': scheduler,
                    'monitor': 'val_loss',
                    'interval': 'epoch',
                    'frequency': 1
                }
            }
        else:
            raise ValueError(f"Unsupported scheduler type: {scheduler_type}")
        
        return {
            'optimizer': optimizer,
            'lr_scheduler': {
                'scheduler': scheduler,
                'interval': 'epoch',
                'frequency': 1
            }
        }
    
    def on_train_epoch_start(self):
        """训练轮次开始时的回调"""
        logging.info(f'{GREEN}=== Training Epoch {self.current_epoch} Started ==={ENDC}')
    
    def on_train_epoch_end(self):
        """训练轮次结束时的回调"""
        # 获取当前学习率
        current_lr = self.optimizers().param_groups[0]['lr']
        logging.info(f'{GREEN}=== Training Epoch {self.current_epoch} Ended ==={ENDC}')
        logging.info(f'{GREEN}Current Learning Rate: {current_lr:.6f}{ENDC}')
        
        # 记录学习率
        self.log('lr', current_lr, on_epoch=True, logger=True, sync_dist=True)
