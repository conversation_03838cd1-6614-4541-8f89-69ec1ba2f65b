flowchart TD
    A["输入 x_t\n(B, num_grasps, C)"] --> B1["GraspNet编码\n(B, num_grasps, 512)"]
    B["文本特征 text_cond\n(B, 512)"] --> B2["文本特征扩展\n(B, num_grasps, 512)"]
    B1 --> C["抓取+文本融合\n(B, num_grasps, 512)"]
    B2 --> C
    D["场景点云特征 scene_cond\n(B, N_points, 512)"] --> E["特征拼接 (cat)\n(B, N_points+1, 512)"]
    B --> E
    E --> F["拼接特征 context_tokens\n(B, N_points+1, 512)"]
    C --> G["reshape为批处理\nh: (B*num_grasps, 512, 1)\ncontext: (B*num_grasps, N_points+1, 512)"]
    F --> G
    G --> H["in_layers(h)\n(B*num_grasps, 512, 1)"]
    H --> I["(可选)位置编码\n(B*num_grasps, 512, 1)"]
    I --> J["for i in nblocks:\nResBlock (B*num_grasps, 512, 1)\nSpatialTransformer (context: (B*num_grasps, N_points+1, 512))"]
    J --> K["out_layers(h)\n(B*num_grasps, d_x, 1)"]
    K --> L["rearrange(h, 'b c l -> b l c').squeeze(1)\n(B*num_grasps, d_x)"]
    L --> M["reshape回原格式\n(B, num_grasps, d_x)"] 