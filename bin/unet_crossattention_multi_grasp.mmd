flowchart TD
    A["输入 x_t\n(B, num_grasps, C)"] --> B1["GraspNet编码\n(B, num_grasps, 512)"]
    B["文本特征 text_cond\n(B, 512)"] --> B2["文本特征扩展\n(B, num_grasps, 512)"]
    B1 --> C["抓取+文本融合\n(B, num_grasps, 512)"]
    B2 --> C
    D["场景点云特征 scene_cond\n(B, N_points, 512)"] --> E["CrossAttentionFusion\nQuery: (B, num_grasps, 512)\nKey/Value: (B, N_points, 512)\nOutput: (B, num_grasps, 512)"]
    C --> E
    E --> F["融合特征 attended_features\n(B, num_grasps, 512)"]
    F --> G["reshape为批处理\nh: (B*num_grasps, 512, 1)\ncontext: (B*num_grasps, 1, 512)"]
    G --> H["in_layers(h)\n(B*num_grasps, 512, 1)"]
    H --> I["(可选)位置编码\n(B*num_grasps, 512, 1)"]
    I --> J["for i in nblocks:\nResBlock (B*num_grasps, 512, 1)\nSpatialTransformer (context: (B*num_grasps, 1, 512))"]
    J --> K["out_layers(h)\n(B*num_grasps, d_x, 1)"]
    K --> L["rearrange(h, 'b c l -> b l c').squeeze(1)\n(B*num_grasps, d_x)"]
    L --> M["reshape回原格式\n(B, num_grasps, d_x)"] 