name: unet

rot_type: ${rot_type}

# # Input/Output dimensions
# input_dims:
#   quat: 23  # 7(translation + quaternion) + 16(qpos)
#   r6d: 25   # 9(translation + r6d) + 16(qpos)
#   default: 23  # 7(translation + quaternion) + 16(qpos)

# Network architecture
d_model: 512
time_embed_mult: 2
nblocks: 4
resblock_dropout: 0.0

# Transformer settings
transformer_num_heads: 8
transformer_dim_head: 64
transformer_dropout: 0.1
transformer_depth: 1
transformer_mult_ff: 2
context_dim: 512

# Backbone settings
backbone:
  name: pointnet2
  use_pooling: false
  layer1:
    npoint: 2048
    radius_list:
    - 0.04
    nsample_list:
    - 64
    mlp_list:
    - 3  # RGB特征是3通道 (xyz坐标会自动处理，这里指定RGB特征的通道数)
    - 64
    - 64
    - 128
  layer2:
    npoint: 1024
    radius_list:
    - 0.1
    nsample_list:
    - 32
    mlp_list:
    - 128
    - 128
    - 128
    - 256
  layer3:
    npoint: 512
    radius_list:
    - 0.2
    nsample_list:
    - 32
    mlp_list:
    - 256
    - 128
    - 128
    - 256
  layer4:
    npoint: 128  # 修改为128个点，作为交叉注意力的Key/Value
    radius_list:
    - 0.3
    nsample_list:
    - 16
    mlp_list:
    - 256
    - 512
    - 512
  use_xyz: true
  normalize_xyz: true

# Other settings
use_position_embedding: false

# Text condition settings
use_text_condition: true
text_dropout_prob: 0.1  # 文本条件随机丢弃概率

# New cross-attention settings
time_embedding_dim: 64  # 时间编码维度
n_points: 128  # 点云特征的点数，与layer4.npoint保持一致

# 多抓取架构配置
multi_grasp:
  # 抓取编码器配置
  grasp_encoder:
    input_dim: null  # 将根据rot_type自动设置 (r6d: 25, quat: 23)
    hidden_dims: [256, 512]  # 隐藏层维度
    output_dim: 512  # 输出维度，与d_model保持一致
    activation: "relu"  # 激活函数
    dropout: 0.1  # Dropout概率

  # 抓取间自注意力配置
  grasp_self_attention:
    enabled: true  # 是否启用抓取间自注意力
    num_heads: 8  # 注意力头数
    dropout: 0.1  # Dropout概率
    layer_norm: true  # 是否使用层归一化

  # 交叉注意力融合配置
  cross_attention_fusion:
    attention_type: "multi_head"  # multi_head, scaled_dot_product
    num_heads: 8  # 注意力头数
    dropout: 0.1  # Dropout概率
    temperature: 1.0  # 注意力温度

  # 并行处理配置
  parallel_processing:
    batch_grasp_processing: true  # 是否批量处理抓取
    memory_efficient: true  # 是否使用内存高效模式
    gradient_checkpointing: false  # 是否使用梯度检查点

device: ${device}